/**
 * Authentication cleanup utilities
 * Helps clean up corrupted or invalid authentication data
 */

/**
 * Clear all authentication-related data from browser storage
 */
export function clearAllAuthData(): void {
  try {
    // Clear localStorage
    const localStorageKeys = [
      'token',
      'user',
      'userProfile',
      'timeTrackerPreferences',
      'categories',
      'timeBlocks'
    ];
    
    localStorageKeys.forEach(key => {
      try {
        localStorage.removeItem(key);
      } catch (error) {
        console.warn(`Failed to remove localStorage key: ${key}`, error);
      }
    });

    // Clear sessionStorage
    const sessionStorageKeys = [
      'lastRoutineConversionTime'
    ];
    
    sessionStorageKeys.forEach(key => {
      try {
        sessionStorage.removeItem(key);
      } catch (error) {
        console.warn(`Failed to remove sessionStorage key: ${key}`, error);
      }
    });

    console.log('Authentication data cleared successfully');
  } catch (error) {
    console.error('Error clearing authentication data:', error);
  }
}

/**
 * Validate if user data is in correct format
 */
export function validateUserData(userData: any): boolean {
  try {
    if (!userData || typeof userData !== 'object') {
      return false;
    }

    // Check if it has required fields
    const hasId = userData.id || (userData.data && userData.data.id);
    if (!hasId) {
      return false;
    }

    return true;
  } catch {
    return false;
  }
}

/**
 * Check if authentication data is corrupted and needs cleanup
 */
export function isAuthDataCorrupted(): boolean {
  try {
    // Check localStorage user data
    const userStr = localStorage.getItem('user');
    if (userStr) {
      try {
        const user = JSON.parse(userStr);
        if (!validateUserData({ data: user })) {
          return true;
        }
      } catch {
        return true;
      }
    }

    // Check localStorage userProfile data
    const profileStr = localStorage.getItem('userProfile');
    if (profileStr) {
      try {
        const profile = JSON.parse(profileStr);
        if (!validateUserData(profile)) {
          return true;
        }
      } catch {
        return true;
      }
    }

    return false;
  } catch {
    return true;
  }
}

/**
 * Perform a complete authentication reset
 * This should be called when authentication is in an unrecoverable state
 */
export function performAuthReset(): void {
  console.log('Performing authentication reset...');
  
  // Clear all data
  clearAllAuthData();
  
  // Force reload to ensure clean state
  if (typeof window !== 'undefined') {
    window.location.href = '/';
  }
}
