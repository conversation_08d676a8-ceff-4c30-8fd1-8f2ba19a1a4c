import { NextRequest } from 'next/server';
import { userRepository } from '@/core/repositories';
import { ApiError } from '@/core/errors/api-error';
import { ErrorCode } from '@/constants/error-codes';
import logger from '@/utils/logger';

/**
 * Rate limiting configuration
 */
export const RATE_LIMIT_CONFIG = {
  // Maximum failed attempts before account lockout
  MAX_FAILED_ATTEMPTS: 5,
  
  // Maximum failed attempts per IP before IP lockout
  MAX_IP_ATTEMPTS: 10,
  
  // Base lockout duration in minutes
  BASE_LOCKOUT_DURATION: 15,
  
  // Maximum lockout duration in minutes
  MAX_LOCKOUT_DURATION: 24 * 60, // 24 hours
  
  // Exponential backoff multiplier
  BACKOFF_MULTIPLIER: 2,
  
  // Time window for IP attempts in minutes
  IP_ATTEMPT_WINDOW: 60,
};

/**
 * Get client IP address from request
 * @param req Next.js request
 * @returns Client IP address
 */
export function getClientIP(req: NextRequest): string {
  // Check various headers for the real IP
  const forwarded = req.headers.get('x-forwarded-for');
  const realIP = req.headers.get('x-real-ip');
  const cfConnectingIP = req.headers.get('cf-connecting-ip');
  
  if (forwarded) {
    // x-forwarded-for can contain multiple IPs, take the first one
    return forwarded.split(',')[0].trim();
  }
  
  if (realIP) {
    return realIP;
  }
  
  if (cfConnectingIP) {
    return cfConnectingIP;
  }
  
  // Fallback to connection remote address
  // NextRequest doesn't have ip property, so we'll use 'unknown' as fallback
  return 'unknown';
}

/**
 * Calculate lockout duration based on failed attempts
 * @param failedAttempts Number of failed attempts
 * @returns Lockout duration in milliseconds
 */
export function calculateLockoutDuration(failedAttempts: number): number {
  const baseMinutes = RATE_LIMIT_CONFIG.BASE_LOCKOUT_DURATION;
  const maxMinutes = RATE_LIMIT_CONFIG.MAX_LOCKOUT_DURATION;
  
  // Exponential backoff: 15min, 30min, 1hr, 2hr, 4hr, 8hr, 16hr, 24hr (max)
  const minutes = Math.min(
    baseMinutes * Math.pow(RATE_LIMIT_CONFIG.BACKOFF_MULTIPLIER, failedAttempts - 1),
    maxMinutes
  );
  
  return minutes * 60 * 1000; // Convert to milliseconds
}

/**
 * Check if an account is currently locked
 * @param accessKey Access key to check
 * @returns Lock status and remaining time
 */
export async function checkAccountLock(accessKey: string): Promise<{
  isLocked: boolean;
  remainingTime?: number;
  reason?: string;
}> {
  try {
    const user = await userRepository.findByAccessKey(accessKey);
    
    if (!user || !user.authAttempts) {
      return { isLocked: false };
    }
    
    const { lockUntil, failedAttempts } = user.authAttempts;
    
    if (lockUntil && new Date() < lockUntil) {
      const remainingTime = lockUntil.getTime() - Date.now();
      return {
        isLocked: true,
        remainingTime,
        reason: `Account locked due to ${failedAttempts} failed attempts`
      };
    }
    
    return { isLocked: false };
  } catch (error) {
    logger.error('Error checking account lock:', error);
    return { isLocked: false };
  }
}

/**
 * Check if an IP is currently rate limited
 * @param req Next.js request
 * @returns Rate limit status
 */
export async function checkIPRateLimit(req: NextRequest): Promise<{
  isLimited: boolean;
  remainingTime?: number;
  reason?: string;
}> {
  const clientIP = getClientIP(req);
  
  try {
    // For IP rate limiting, we'll use a simple in-memory store for now
    // In production, you might want to use Redis or a database
    const users = await userRepository.findUsersWithIPAttempts(clientIP);
    
    let totalAttempts = 0;
    let latestLockUntil: Date | null = null;
    
    for (const user of users) {
      const ipAttempt = user.authAttempts.ipAttempts.get(clientIP);
      if (ipAttempt) {
        // Check if attempt is within the time window
        const timeSinceLastAttempt = Date.now() - ipAttempt.lastAttempt.getTime();
        const windowMs = RATE_LIMIT_CONFIG.IP_ATTEMPT_WINDOW * 60 * 1000;
        
        if (timeSinceLastAttempt <= windowMs) {
          totalAttempts += ipAttempt.count;
        }
        
        if (ipAttempt.lockUntil && (!latestLockUntil || ipAttempt.lockUntil > latestLockUntil)) {
          latestLockUntil = ipAttempt.lockUntil;
        }
      }
    }
    
    // Check if IP is currently locked
    if (latestLockUntil && new Date() < latestLockUntil) {
      const remainingTime = latestLockUntil.getTime() - Date.now();
      return {
        isLimited: true,
        remainingTime,
        reason: `IP rate limited due to ${totalAttempts} failed attempts`
      };
    }
    
    // Check if IP has exceeded attempt limit
    if (totalAttempts >= RATE_LIMIT_CONFIG.MAX_IP_ATTEMPTS) {
      return {
        isLimited: true,
        reason: `IP rate limited: ${totalAttempts} attempts in ${RATE_LIMIT_CONFIG.IP_ATTEMPT_WINDOW} minutes`
      };
    }
    
    return { isLimited: false };
  } catch (error) {
    logger.error('Error checking IP rate limit:', error);
    return { isLimited: false };
  }
}

/**
 * Record a failed authentication attempt
 * @param accessKey Access key that failed
 * @param req Next.js request for IP tracking
 */
export async function recordFailedAttempt(accessKey: string, req: NextRequest): Promise<void> {
  const clientIP = getClientIP(req);
  
  try {
    const user = await userRepository.findByAccessKey(accessKey);
    
    if (!user) {
      logger.warn(`Attempted to record failed attempt for non-existent access key: ${accessKey}`);
      return;
    }
    
    // Initialize authAttempts if not present
    if (!user.authAttempts) {
      user.authAttempts = {
        failedAttempts: 0,
        ipAttempts: new Map(),
      };
    }
    
    // Update account-level failed attempts
    user.authAttempts.failedAttempts += 1;
    user.authAttempts.lastFailedAttempt = new Date();
    
    // Set account lockout if threshold exceeded
    if (user.authAttempts.failedAttempts >= RATE_LIMIT_CONFIG.MAX_FAILED_ATTEMPTS) {
      const lockoutDuration = calculateLockoutDuration(user.authAttempts.failedAttempts);
      user.authAttempts.lockUntil = new Date(Date.now() + lockoutDuration);
      
      logger.warn(`Account locked for access key ${accessKey}: ${user.authAttempts.failedAttempts} failed attempts`);
    }
    
    // Update IP-level attempts
    const currentIPAttempt = user.authAttempts.ipAttempts.get(clientIP) || {
      count: 0,
      lastAttempt: new Date(),
    };
    
    currentIPAttempt.count += 1;
    currentIPAttempt.lastAttempt = new Date();
    
    // Set IP lockout if threshold exceeded
    if (currentIPAttempt.count >= RATE_LIMIT_CONFIG.MAX_IP_ATTEMPTS) {
      const lockoutDuration = calculateLockoutDuration(currentIPAttempt.count);
      currentIPAttempt.lockUntil = new Date(Date.now() + lockoutDuration);
      
      logger.warn(`IP locked for ${clientIP}: ${currentIPAttempt.count} failed attempts`);
    }
    
    user.authAttempts.ipAttempts.set(clientIP, currentIPAttempt);
    
    // Save the updated user
    await user.save();
    
    logger.info(`Recorded failed attempt for access key ${accessKey} from IP ${clientIP}`);
  } catch (error) {
    logger.error('Error recording failed attempt:', error);
  }
}

/**
 * Reset failed attempts for successful authentication
 * @param accessKey Access key that successfully authenticated
 */
export async function resetFailedAttempts(accessKey: string): Promise<void> {
  try {
    const user = await userRepository.findByAccessKey(accessKey);
    
    if (!user) {
      return;
    }
    
    // Reset account-level attempts
    if (user.authAttempts) {
      user.authAttempts.failedAttempts = 0;
      user.authAttempts.lastFailedAttempt = undefined;
      user.authAttempts.lockUntil = undefined;
      
      await user.save();
      
      logger.info(`Reset failed attempts for access key ${accessKey}`);
    }
  } catch (error) {
    logger.error('Error resetting failed attempts:', error);
  }
}

/**
 * Validate authentication attempt against rate limits
 * @param accessKey Access key attempting authentication
 * @param req Next.js request
 * @throws ApiError if rate limited
 */
export async function validateAuthAttempt(accessKey: string, req: NextRequest): Promise<void> {
  // Check account-level lock
  const accountLock = await checkAccountLock(accessKey);
  if (accountLock.isLocked) {
    const remainingMinutes = accountLock.remainingTime ? Math.ceil(accountLock.remainingTime / (60 * 1000)) : 0;
    throw ApiError.tooManyRequests(
      `Account temporarily locked. Try again in ${remainingMinutes} minutes.`,
      ErrorCode.ACCOUNT_LOCKED
    );
  }
  
  // Check IP-level rate limit
  const ipLimit = await checkIPRateLimit(req);
  if (ipLimit.isLimited) {
    const remainingMinutes = ipLimit.remainingTime ? Math.ceil(ipLimit.remainingTime / (60 * 1000)) : 0;
    throw ApiError.tooManyRequests(
      `Too many failed attempts from this IP. Try again in ${remainingMinutes} minutes.`,
      ErrorCode.IP_RATE_LIMITED
    );
  }
}
