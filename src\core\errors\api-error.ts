import { ErrorCode } from '@/constants/error-codes';

/**
 * Custom API error class
 * Used to create standardized error responses
 */
export class ApiError extends Error {
  code: ErrorCode;
  statusCode: number;

  constructor(code: ErrorCode, message: string, statusCode: number = 500) {
    super(message);
    this.name = 'ApiError';
    this.code = code;
    this.statusCode = statusCode;
    
    // This is needed because we're extending a built-in class
    Object.setPrototypeOf(this, ApiError.prototype);
  }

  /**
   * Create a bad request error (400)
   */
  static badRequest(message: string = 'Bad request', code: ErrorCode = ErrorCode.BAD_REQUEST): ApiError {
    return new ApiError(code, message, 400);
  }

  /**
   * Create an unauthorized error (401)
   */
  static unauthorized(message: string = 'Unauthorized', code: ErrorCode = ErrorCode.UNAUTHORIZED): ApiError {
    return new ApiError(code, message, 401);
  }

  /**
   * Create a forbidden error (403)
   */
  static forbidden(message: string = 'Forbidden', code: ErrorCode = ErrorCode.FORBIDDEN): ApiError {
    return new ApiError(code, message, 403);
  }

  /**
   * Create a not found error (404)
   */
  static notFound(message: string = 'Not found', code: ErrorCode = ErrorCode.NOT_FOUND): ApiError {
    return new ApiError(code, message, 404);
  }

  /**
   * Create a conflict error (409)
   */
  static conflict(message: string = 'Conflict', code: ErrorCode = ErrorCode.CONFLICT): ApiError {
    return new ApiError(code, message, 409);
  }

  /**
   * Create an internal server error (500)
   */
  static internal(message: string = 'Internal server error', code: ErrorCode = ErrorCode.INTERNAL_SERVER_ERROR): ApiError {
    return new ApiError(code, message, 500);
  }

  /**
   * Create a too many requests error (429)
   */
  static tooManyRequests(message: string = 'Too many requests', code: ErrorCode = ErrorCode.TOO_MANY_REQUESTS): ApiError {
    return new ApiError(code, message, 429);
  }
}
