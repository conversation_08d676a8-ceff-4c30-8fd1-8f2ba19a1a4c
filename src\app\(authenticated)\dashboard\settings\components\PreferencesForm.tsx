"use client"

import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { Button } from '@/components/ui/button';
import { Form } from '@/components/ui/form';
import { Card, CardContent } from '@/components/ui/card';
import { toast } from 'sonner';
import { preferencesSchema, PreferencesFormValues } from '../schemas';
import { TimeIntervalPreference } from './preferences/TimeIntervalPreference';
import { TimeDisplayPreference } from './preferences/TimeDisplayPreference';
import { TimeFormatPreference } from './preferences/TimeFormatPreference';
import { SyncPreference } from './preferences/SyncPreference';
import { CustomTimeBlocksPreference } from './preferences/CustomTimeBlocksPreference';

// Define the valid time interval values
type TimeInterval = '15' | '30' | '60' | '120' | '180' | '240' | '300' | '360' | '420' | '480' | '540' | '600' | '660' | '720';
type TimeFormat = '12' | '24';

interface PreferencesFormProps {
  initialData: {
    timeInterval: TimeInterval;
    startHour: string;
    endHour: string;
    timeFormat: TimeFormat;
    darkMode: boolean;
    syncEnabled?: boolean;
    customTimeBlocks?: Array<{ startTime: string; endTime: string }>;
    useCustomTimeBlocks?: boolean;
  };
  onSavePreferences: (values: PreferencesFormValues) => Promise<boolean>;
}

export function PreferencesForm({ initialData, onSavePreferences }: PreferencesFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formKey, setFormKey] = useState(0); // Add key to force form reset

  const form = useForm<PreferencesFormValues>({
    resolver: zodResolver(preferencesSchema),
    defaultValues: {
      timeInterval: initialData.timeInterval as TimeInterval,
      startHour: initialData.startHour,
      endHour: initialData.endHour,
      timeFormat: initialData.timeFormat as TimeFormat,
      darkMode: initialData.darkMode,
      syncEnabled: initialData.syncEnabled ?? true,
      customTimeBlocks: initialData.customTimeBlocks || [],
      useCustomTimeBlocks: initialData.useCustomTimeBlocks || false,
    },
  });

  // Reset form when initialData changes
  useEffect(() => {
    setFormKey(prev => prev + 1); // Force form reset
    form.reset({
      timeInterval: initialData.timeInterval as TimeInterval,
      startHour: initialData.startHour,
      endHour: initialData.endHour,
      timeFormat: initialData.timeFormat as TimeFormat,
      darkMode: initialData.darkMode,
      syncEnabled: initialData.syncEnabled ?? true,
      customTimeBlocks: initialData.customTimeBlocks || [],
      useCustomTimeBlocks: initialData.useCustomTimeBlocks || false,
    });
  }, [initialData, form]);

  const onSubmit = async (values: PreferencesFormValues) => {
    try {
      setIsSubmitting(true);
      console.log('Submitting preferences:', values);

      // Validate custom time blocks if enabled
      if (values.useCustomTimeBlocks && (!values.customTimeBlocks || values.customTimeBlocks.length === 0)) {
        toast.error('Please add at least one custom time block when using custom time blocks');
        return;
      }

      const success = await onSavePreferences(values);
      if (success) {
        toast.success('Preferences saved successfully');
        // Force form reset to ensure clean state
        setFormKey(prev => prev + 1);
      } else {
        toast.error('Failed to save preferences');
      }
    } catch (error) {
      console.error('Error saving preferences:', error);
      toast.error('An error occurred while saving preferences');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Card className="shadow-sm">
      <CardContent className="pt-3 px-4">
        <Form {...form}>
          <form key={formKey} onSubmit={form.handleSubmit(onSubmit)} className="space-y-3">
            <TimeIntervalPreference form={form} />
            <CustomTimeBlocksPreference form={form} />
            <TimeDisplayPreference form={form} />
            <TimeFormatPreference form={form} />
            <SyncPreference form={form} />

            <div className="flex justify-end pt-1">
              <Button
                type="submit"
                disabled={isSubmitting}
                className="px-4 py-1 h-8 border text-sm"
              >
                {isSubmitting ? 'Saving...' : 'Save Preferences'}
              </Button>
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}
