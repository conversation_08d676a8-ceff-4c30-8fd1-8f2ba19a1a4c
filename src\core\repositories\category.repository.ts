import Category, { ICategory } from '@/db/models/Category';
import connectDB from '@/config/database';
import { CreateCategoryRequest, UpdateCategoryRequest } from '@/types/category';
import mongoose from 'mongoose';
import { ApiError } from '@/core/errors/api-error';
import { ErrorCode } from '@/constants/error-codes';
import logger from '@/utils/logger';

/**
 * Category repository for database operations
 */
export class CategoryRepository {
  /**
   * Find all categories for a user
   * @param userId User ID
   * @returns Array of categories
   */
  async findAllByUserId(userId: string): Promise<ICategory[]> {
    try {
      await connectDB();
      return Category.find({ userId });
    } catch (error) {
      logger.error('Find all categories error:', error);
      throw new ApiError(ErrorCode.DATABASE_ERROR, 'Database error', 500);
    }
  }

  /**
   * Find category by ID
   * @param id Category ID
   * @returns Category or null
   */
  async findById(id: string): Promise<ICategory | null> {
    try {
      await connectDB();
      return Category.findById(id);
    } catch (error) {
      logger.error('Find category by ID error:', error);
      throw new ApiError(ErrorCode.DATABASE_ERROR, 'Database error', 500);
    }
  }

  /**
   * Create new category
   * @param userId User ID
   * @param data Category data
   * @returns Created category
   */
  async create(userId: string, data: CreateCategoryRequest): Promise<ICategory> {
    try {
      await connectDB();
      const category = new Category({
        userId,
        ...data
      });
      return category.save();
    } catch (error) {
      logger.error('Create category error:', error);
      
      if (error instanceof mongoose.Error.ValidationError) {
        throw new ApiError(ErrorCode.VALIDATION_ERROR, error.message, 400);
      }
      
      throw new ApiError(ErrorCode.DATABASE_ERROR, 'Database error', 500);
    }
  }

  /**
   * Update category
   * @param id Category ID
   * @param data Category data
   * @returns Updated category
   */
  async update(id: string, data: UpdateCategoryRequest): Promise<ICategory | null> {
    try {
      await connectDB();
      return Category.findByIdAndUpdate(id, data, { new: true });
    } catch (error) {
      logger.error('Update category error:', error);
      
      if (error instanceof mongoose.Error.ValidationError) {
        throw new ApiError(ErrorCode.VALIDATION_ERROR, error.message, 400);
      }
      
      throw new ApiError(ErrorCode.DATABASE_ERROR, 'Database error', 500);
    }
  }

  /**
   * Delete category
   * @param id Category ID
   * @returns Deleted category
   */
  async delete(id: string): Promise<ICategory | null> {
    try {
      await connectDB();
      return Category.findByIdAndDelete(id);
    } catch (error) {
      logger.error('Delete category error:', error);
      throw new ApiError(ErrorCode.DATABASE_ERROR, 'Database error', 500);
    }
  }
}
