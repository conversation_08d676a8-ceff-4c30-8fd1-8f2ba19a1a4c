import { categoryRepository, timeBlockRepository } from '@/core/repositories';
import { CreateCategoryRequest, UpdateCategoryRequest } from '@/types/category';
import { ApiError } from '@/core/errors/api-error';
import { ErrorCode } from '@/constants/error-codes';
import logger from '@/utils/logger';
import mongoose from 'mongoose';

/**
 * Category service
 */
export class CategoryService {
  /**
   * Get all categories for a user
   * @param userId User ID
   * @returns Array of categories
   */
  async getAllCategories(userId: string) {
    try {
      const categories = await categoryRepository.findAllByUserId(userId);

      // If no categories found, return an empty array
      if (!categories || categories.length === 0) {
        logger.info(`No categories found for user ${userId}`);
        return [];
      }

      // Transform the MongoDB documents to ensure each has a proper id field
      return categories.map(category => {
        const categoryObj = category.toObject();
        const categoryId = category._id instanceof mongoose.Types.ObjectId
          ? category._id.toString()
          : typeof category._id === 'string'
            ? category._id
            : String(category._id);

        return {
          ...categoryObj,
          id: categoryId
        };
      });
    } catch (error) {
      logger.error('Fetch categories error:', error);
      // Return empty array instead of throwing error to prevent frontend issues
      logger.info('Returning empty array due to error');
      return [];
    }
  }

  /**
   * Create a new category
   * @param userId User ID
   * @param data Category data
   * @returns Created category
   */
  async createCategory(userId: string, data: CreateCategoryRequest) {
    try {
      const { name, color } = data;

      if (!name || !color) {
        throw ApiError.badRequest('Name and color are required', ErrorCode.VALIDATION_ERROR);
      }

      const category = await categoryRepository.create(userId, { name, color });

      const categoryObj = category.toObject();
      const categoryId = category._id instanceof mongoose.Types.ObjectId
        ? category._id.toString()
        : typeof category._id === 'string'
          ? category._id
          : String(category._id);

      return {
        ...categoryObj,
        id: categoryId
      };
    } catch (error) {
      logger.error('Create category error:', error);

      if (error instanceof ApiError) {
        throw error;
      }

      throw ApiError.internal('Server error', ErrorCode.DATABASE_ERROR);
    }
  }

  /**
   * Update a category
   * @param id Category ID
   * @param userId User ID
   * @param data Category data
   * @returns Updated category
   */
  async updateCategory(id: string, userId: string, data: UpdateCategoryRequest) {
    try {
      // Verify category exists and belongs to user
      const existingCategory = await categoryRepository.findById(id);
      if (!existingCategory) {
        throw ApiError.notFound('Category not found', ErrorCode.CATEGORY_NOT_FOUND);
      }

      if (existingCategory.userId.toString() !== userId) {
        throw ApiError.forbidden('Category does not belong to user', ErrorCode.FORBIDDEN);
      }

      const category = await categoryRepository.update(id, data);
      if (!category) {
        throw ApiError.notFound('Category not found', ErrorCode.CATEGORY_NOT_FOUND);
      }

      const categoryObj = category.toObject();
      const categoryId = category._id instanceof mongoose.Types.ObjectId
        ? category._id.toString()
        : typeof category._id === 'string'
          ? category._id
          : String(category._id);

      return {
        ...categoryObj,
        id: categoryId
      };
    } catch (error) {
      logger.error('Update category error:', error);

      if (error instanceof ApiError) {
        throw error;
      }

      throw ApiError.internal('Server error', ErrorCode.DATABASE_ERROR);
    }
  }

  /**
   * Delete a category
   * @param id Category ID
   * @param userId User ID
   * @returns Success or error
   */
  async deleteCategory(id: string, userId: string) {
    try {
      // Verify category exists and belongs to user
      const existingCategory = await categoryRepository.findById(id);
      if (!existingCategory) {
        throw ApiError.notFound('Category not found', ErrorCode.CATEGORY_NOT_FOUND);
      }

      if (existingCategory.userId.toString() !== userId) {
        throw ApiError.forbidden('Category does not belong to user', ErrorCode.FORBIDDEN);
      }

      // Delete category
      await categoryRepository.delete(id);

      // TODO: Update time blocks that use this category to use a default category
      // This would be implemented in a real-world application

      return { success: true };
    } catch (error) {
      logger.error('Delete category error:', error);

      if (error instanceof ApiError) {
        throw error;
      }

      throw ApiError.internal('Server error', ErrorCode.DATABASE_ERROR);
    }
  }
}
