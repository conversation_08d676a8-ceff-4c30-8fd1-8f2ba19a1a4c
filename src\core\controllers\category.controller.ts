import { NextResponse } from 'next/server';
import { categoryService } from '@/core/services';
import { CreateCategoryRequest, UpdateCategoryRequest } from '@/types/category';
import { successResponse, errorResponse } from '@/utils/response';
import { ErrorCode } from '@/constants/error-codes';
import logger from '@/utils/logger';
import { ApiError } from '@/core/errors/api-error';

/**
 * Category controller
 */
export class CategoryController {
  /**
   * Get all categories for a user
   * @param userId User ID
   * @returns Response with categories or error
   */
  async getAllCategories(userId: string): Promise<NextResponse> {
    try {
      const categories = await categoryService.getAllCategories(userId);
      // Ensure we always return an array, even if the service returns null or undefined
      return successResponse(Array.isArray(categories) ? categories : []);
    } catch (error) {
      logger.error('Get categories controller error:', error);

      if (error instanceof ApiError) {
        return errorResponse(error.message, error.code, error.statusCode);
      }

      // Return empty array instead of error response to prevent frontend issues
      logger.info('Returning empty array due to error in controller');
      return successResponse([]);
    }
  }

  /**
   * Create a new category
   * @param userId User ID
   * @param data Category data
   * @returns Response with created category or error
   */
  async createCategory(userId: string, data: CreateCategoryRequest): Promise<NextResponse> {
    try {
      const category = await categoryService.createCategory(userId, data);
      return successResponse(category, 201);
    } catch (error) {
      logger.error('Create category controller error:', error);

      if (error instanceof ApiError) {
        return errorResponse(error.message, error.code, error.statusCode);
      }

      return errorResponse(
        'Server error',
        ErrorCode.INTERNAL_SERVER_ERROR,
        500
      );
    }
  }

  /**
   * Update a category
   * @param id Category ID
   * @param userId User ID
   * @param data Category data
   * @returns Response with updated category or error
   */
  async updateCategory(id: string, userId: string, data: UpdateCategoryRequest): Promise<NextResponse> {
    try {
      const category = await categoryService.updateCategory(id, userId, data);
      return successResponse(category);
    } catch (error) {
      logger.error('Update category controller error:', error);

      if (error instanceof ApiError) {
        return errorResponse(error.message, error.code, error.statusCode);
      }

      return errorResponse(
        'Server error',
        ErrorCode.INTERNAL_SERVER_ERROR,
        500
      );
    }
  }

  /**
   * Delete a category
   * @param id Category ID
   * @param userId User ID
   * @returns Response with success message or error
   */
  async deleteCategory(id: string, userId: string): Promise<NextResponse> {
    try {
      await categoryService.deleteCategory(id, userId);
      return successResponse({ message: 'Category deleted successfully' });
    } catch (error) {
      logger.error('Delete category controller error:', error);

      if (error instanceof ApiError) {
        return errorResponse(error.message, error.code, error.statusCode);
      }

      return errorResponse(
        'Server error',
        ErrorCode.INTERNAL_SERVER_ERROR,
        500
      );
    }
  }
}
