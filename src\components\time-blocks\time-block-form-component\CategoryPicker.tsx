"use client"

import { useState, useRef, useEffect } from 'react';
import { toast } from 'sonner';
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { HexColorPicker } from 'react-colorful';
import { Plus, Trash2 } from 'lucide-react';
import { useCategories } from '@/hooks/use-categories';
import { UseFormReturn } from 'react-hook-form';
import { cn } from '@/lib/utils';
import { getCategoryStyles } from '../category-styles';

interface CategoryPickerProps {
  form: UseFormReturn<any>;
  onCreateCategory?: (name: string, color: string) => Promise<any>;
}

export function CategoryPicker({ form, onCreateCategory }: CategoryPickerProps) {
  const [isAddingCategory, setIsAddingCategory] = useState(false);
  const [newCategoryName, setNewCategoryName] = useState('');
  const [newCategoryColor, setNewCategoryColor] = useState('#3b82f6');
  const [showColorPicker, setShowColorPicker] = useState(false);
  const [confirmDeleteId, setConfirmDeleteId] = useState<string | null>(null);
  const colorPickerRef = useRef<HTMLDivElement>(null);
  const { categories, deleteCategory, refresh, createCategory } = useCategories();

  // Set the first category as default if no category is selected
  useEffect(() => {
    const currentCategory = form.getValues('category');
    if ((!currentCategory || currentCategory === '') && categories.length > 0) {
      form.setValue('category', categories[0].id);
    }
  }, [categories, form]);

  // Make sure we always have a valid category selected
  useEffect(() => {
    // If categories are loaded and no category is selected, select the first one
    if (categories.length > 0) {
      const currentCategory = form.getValues('category');

      // Check if the current category exists in the categories list
      const categoryExists = categories.some(cat => cat.id === currentCategory);

      if (!categoryExists) {
        // If the current category doesn't exist, select the first one
        form.setValue('category', categories[0].id);
      }
    }
  }, [categories, form]);

  const handleAddCategory = async () => {
    if (!newCategoryName.trim() || !newCategoryColor) return;

    try {
      // Use the parent's createCategory function if provided, otherwise use the hook's
      const createFn = onCreateCategory || createCategory;
      const newCat = await createFn(newCategoryName.trim(), newCategoryColor);
      
      if (newCat) {
        // Force refresh the categories list to ensure it's up to date
        await refresh(true);
        
        setIsAddingCategory(false);
        setNewCategoryName('');
        setNewCategoryColor('#3b82f6');
        
        // The parent component is responsible for selecting the new category
        // through the onCreateCategory callback
      }
    } catch (error) {
      console.error('Failed to create category:', error);
      toast.error('Failed to create category');
    }
  };

  const handleDeleteCategory = async (id: string) => {
    try {
      const success = await deleteCategory(id);
      if (success) {
        setConfirmDeleteId(null);
        // If the deleted category was selected, reset the form field
        const deletedCategory = categories.find(cat => cat.id === id);
        if (deletedCategory && form.getValues('category') === deletedCategory.id) {
          form.setValue('category', '');
        }
      }
    } catch (error) {
      console.error('Failed to delete category:', error);
    }
  };

  return (
    <div className="space-y-1">
      <div className="flex items-center justify-between">
        <FormLabel className="text-sm font-medium">Category</FormLabel>
        <Button
          type="button"
          variant="ghost"
          size="sm"
          className="h-7 px-2 text-xs rounded-md"
          onClick={() => setIsAddingCategory(true)}
        >
          <Plus className="h-3.5 w-3.5 mr-1.5" /> New Category
        </Button>
      </div>

      <FormField
        control={form.control}
        name="category"
        render={({ field }) => (
          <FormItem className="mb-0">
            <Select
              onValueChange={(val) => {
                // Always set ObjectId as value
                field.onChange(val);
              }}
              value={field.value}
            >
              <FormControl>
                <SelectTrigger className="text-sm h-9 border-input focus-visible:ring-1">
                  <SelectValue placeholder="Select a category">
                    {field.value && (
                      <div className="flex items-center gap-2">
                        <div
                          className={cn(
                            "w-4 h-4 rounded-full flex-shrink-0 border border-border/50",
                            Array.isArray(categories) && field.value
                              ? getCategoryStyles(categories.find(c => c.id === field.value)?.color || '#6b7280').background
                              : "bg-gray-500"
                          )}
                          style={{
                            backgroundColor: Array.isArray(categories) && field.value
                              ? categories.find(c => c.id === field.value)?.color || '#6b7280'
                              : "#6b7280"
                          }}
                        />
                        <span className="font-medium truncate">
                          {Array.isArray(categories) && field.value && typeof field.value === 'string'
                            ? (categories.find(c => c.id === field.value)?.name || field.value)
                            : ''}
                        </span>
                      </div>
                    )}
                  </SelectValue>
                </SelectTrigger>
              </FormControl>
              <SelectContent className="max-h-[220px]">
                <div className="max-h-[180px] overflow-y-auto pr-1">
                  {Array.isArray(categories) && categories.length > 0 ? (
                    categories.map((category) => (
                      <div key={category.id} className="flex items-center justify-between group relative pr-8 my-0.5">
                        <SelectItem
                          key={category.id}
                          value={category.id}
                          className="flex items-center gap-2"
                        >
                          <div className="flex items-center gap-2">
                            <div
                              className={cn(
                                "h-4 w-4 rounded-full border border-border/50"
                              )}
                              style={{
                                backgroundColor: category.color || '#6b7280'
                              }}
                            />
                            <span className="font-medium truncate">{category.name}</span>
                          </div>
                        </SelectItem>
                        {confirmDeleteId === category.id ? (
                          <div className="absolute right-0 top-0 bottom-0 flex items-center gap-1 pr-1 bg-background z-10">
                            <Button
                              type="button"
                              variant="ghost"
                              size="sm"
                              className="h-6 px-2 text-xs"
                              onClick={(e) => {
                                e.stopPropagation();
                                setConfirmDeleteId(null);
                              }}
                            >
                              Cancel
                            </Button>
                            <Button
                              type="button"
                              variant="destructive"
                              size="sm"
                              className="h-6 px-2 text-xs"
                              onClick={(e) => {
                                e.stopPropagation();
                                handleDeleteCategory(category.id);
                              }}
                            >
                              Delete
                            </Button>
                          </div>
                        ) : (
                          <Button
                            type="button"
                            variant="ghost"
                            size="sm"
                            className="absolute right-1 top-1/2 -translate-y-1/2 h-6 w-6 p-0"
                            onClick={(e) => {
                              e.stopPropagation();
                              setConfirmDeleteId(category.id);
                            }}
                          >
                            <Trash2 className="h-3.5 w-3.5 text-destructive" />
                          </Button>
                        )}
                      </div>
                    ))
                  ) : (
                    <div className="p-3 text-sm text-muted-foreground text-center">
                      {Array.isArray(categories) && categories.length === 0
                        ? "No categories found. Add one."
                        : "Loading categories..."}
                    </div>
                  )}
                </div>
              </SelectContent>
            </Select>
            <FormMessage className="text-xs" />
          </FormItem>
        )}
      />

      {/* Add Category Panel */}
      {isAddingCategory && (
        <div className="p-3 border rounded-md space-y-3 bg-card shadow-sm mt-1 relative">
          <div className="flex items-center justify-between">
            <h4 className="font-medium text-sm">New Category</h4>
            <button
              type="button"
              onClick={() => setIsAddingCategory(false)}
              className="text-muted-foreground hover:text-foreground transition-colors"
              aria-label="Close"
            >
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <line x1="18" y1="6" x2="6" y2="18"></line>
                <line x1="6" y1="6" x2="18" y2="18"></line>
              </svg>
            </button>
          </div>

          <div className="flex items-center gap-3">
            <div className="flex-grow">
              <Input
                placeholder="Category name"
                value={newCategoryName}
                onChange={(e) => setNewCategoryName(e.target.value)}
                className="text-sm h-9"
                autoFocus
              />
            </div>
            <div className="relative">
              <div
                className={cn(
                  "w-9 h-9 rounded-md cursor-pointer border shadow-sm flex items-center justify-center hover:border-primary transition-colors"
                )}
                style={{
                  backgroundColor: newCategoryColor,
                  borderColor: 'hsl(var(--border) / 0.5)'
                }}
                onClick={(e) => {
                  e.stopPropagation();
                  setShowColorPicker(!showColorPicker);
                }}
              >
                {showColorPicker && (
                  <div
                    ref={colorPickerRef}
                    className="absolute z-50 right-0 top-full mt-1 p-3 bg-popover rounded-lg shadow-lg border"
                    onClick={(e) => e.stopPropagation()}
                  >
                    <HexColorPicker
                      color={newCategoryColor}
                      onChange={(color) => {
                        setNewCategoryColor(color);
                      }}
                      style={{ width: '200px', height: '150px' }}
                    />
                    <div className="mt-3 flex justify-between">
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        className="h-8 text-xs"
                        onClick={(e) => {
                          e.stopPropagation();
                          setNewCategoryColor('#3b82f6');
                        }}
                      >
                        Reset
                      </Button>
                      <Button
                        type="button"
                        variant="default"
                        size="sm"
                        className="h-8 text-xs"
                        onClick={(e) => {
                          e.stopPropagation();
                          setShowColorPicker(false);
                        }}
                      >
                        Done
                      </Button>
                    </div>
                  </div>
                )}
              </div>
            </div>
            <Button
              type="button"
              variant="default"
              size="sm"
              className="h-9 text-sm"
              onClick={handleAddCategory}
              disabled={!newCategoryName.trim()}
            >
              Add
            </Button>
          </div>
        </div>
      )}
    </div>
  );
}
