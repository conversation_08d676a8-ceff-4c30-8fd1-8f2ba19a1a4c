import { NextRequest, NextResponse } from 'next/server';
import { verifyAuth } from '@/utils/auth';
import { ApiError } from '@/core/errors/api-error';
import { ErrorCode } from '@/constants/error-codes';

/**
 * Authentication middleware
 * Verifies that the request has a valid JWT token
 *
 * @param req Next.js request
 * @returns NextResponse or null
 */
export function authMiddleware(req: NextRequest): NextResponse | null {
  try {
    const { authenticated, user } = verifyAuth(req);

    if (!authenticated) {
      throw new ApiError(ErrorCode.UNAUTHORIZED, 'Authentication required');
    }

    return null; // Continue to the route handler
  } catch (error) {
    if (error instanceof ApiError) {
      return NextResponse.json(
        {
          success: false,
          error: {
            code: error.code,
            message: error.message
          }
        },
        { status: error.statusCode }
      );
    }

    // Fallback for unexpected errors
    return NextResponse.json(
      {
        success: false,
        error: {
          code: ErrorCode.INTERNAL_SERVER_ERROR,
          message: 'Internal server error'
        }
      },
      { status: 500 }
    );
  }
}
