import mongoose from 'mongoose';
// Removed: import Routine, { IRoutine } from '@/db/models/Routine';
// Removed: import Category from '@/db/models/Category';
import { ApiRoutine } from '@/types/routine';
import connectDB from '@/config/database'; // Corrected import path
import logger from '@/utils/logger';

// Define a minimal schema for the 'Routine' model to interact with the existing collection
// Using strict: false allows flexibility with existing document structures in the DB.
const routineMongooseSchema = new mongoose.Schema({}, { strict: false, collection: 'routines' });
const RoutineModel = mongoose.models.Routine || mongoose.model('Routine', routineMongooseSchema);

export class RoutineRepository {
  private async ensureConnected() {
    if (mongoose.connection.readyState === 0) {
      await connectDB();
    }
  }

  private transformRoutine(routineDoc: any): ApiRoutine {
    const routineObject = routineDoc.toObject ? routineDoc.toObject() : routineDoc;

    // Ensure categoryData is populated correctly
    let categoryData;
    if (routineObject.category && routineObject.category.name && routineObject.category.color) {
      // This case handles when category is already populated by $lookup
      categoryData = {
        id: routineObject.category._id?.toString() || routineObject.category.id?.toString(), // Handle both direct field and populated
        name: routineObject.category.name,
        color: routineObject.category.color,
      };
    } else if (routineObject.categoryData) {
        // This handles the case where categoryData might be manually attached
        categoryData = routineObject.categoryData;
    }


    return {
      id: routineObject._id.toString(),
      // userId: routineObject.userId.toString(), // Removed
      title: routineObject.title,
      note: routineObject.note, // Changed from description
      startTime: routineObject.startTime,
      endTime: routineObject.endTime,
      days: routineObject.days,
      categoryId: routineObject.category?._id?.toString() || routineObject.categoryId?.toString() || routineObject.category?.toString(),
      categoryData: categoryData,
      // isEnabled: routineObject.isEnabled !== undefined ? routineObject.isEnabled : true, // Removed
      // createdAt: routineObject.createdAt?.toISOString() || new Date().toISOString(), // Removed
      // updatedAt: routineObject.updatedAt?.toISOString() || new Date().toISOString(), // Removed
    };
  }

  async findAllByUserId(userId: string): Promise<ApiRoutine[]> {
    await this.ensureConnected();
    logger.info(`Fetching routines for userId: ${userId}`);
    try {
      const routines = await RoutineModel.aggregate([ // Changed Routine to RoutineModel
        { $match: { userId: new mongoose.Types.ObjectId(userId) } },
        {
          $lookup: {
            from: 'categories', // The actual name of the categories collection in MongoDB
            localField: 'categoryId',
            foreignField: '_id',
            as: 'categoryInfo',
          },
        },
        {
          $unwind: {
            path: '$categoryInfo',
            preserveNullAndEmptyArrays: true, // Keep routines even if category is not found
          },
        },
        {
          $addFields: {
            categoryData: { // Adding categoryData directly in the format ApiRoutine expects
              id: '$categoryInfo._id',
              name: '$categoryInfo.name',
              color: '$categoryInfo.color',
            },
          },
        },
        {
          // Project to match the structure expected by transformRoutine,
          // ensuring categoryId is correctly passed if categoryInfo is null
          $project: {
            _id: 1,
            // userId: 1, // Removed
            title: 1,
            note: 1, // Changed from description: 1
            startTime: 1,
            endTime: 1,
            days: 1,
            categoryId: '$categoryId', // pass original categoryId
            category: '$categoryInfo', // for transformRoutine (it expects category.name, category.color)
                                      // if categoryInfo is null, transformRoutine should handle it
            // isEnabled: 1, // Removed
            // createdAt: 1, // Removed
            // updatedAt: 1, // Removed
            // categoryData is already added by $addFields
          }
        }
      ]);

      if (!routines) {
        logger.info(`No routines found for userId: ${userId}`);
        return [];
      }
      
      logger.info(`Found ${routines.length} routines before transformation for userId: ${userId}`);
      const transformedRoutines = routines.map(routine => this.transformRoutine(routine));
      logger.info(`Successfully fetched and transformed ${transformedRoutines.length} routines for userId: ${userId}`);
      return transformedRoutines;

    } catch (error) {
      logger.error('Error fetching routines by userId:', error);
      throw new Error('Failed to fetch routines.');
    }
  }
}
