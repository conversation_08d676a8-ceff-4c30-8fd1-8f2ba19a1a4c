"use client";

import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import {
  ArrowRight,
  Clock,
  Shield,
  Target,
  Star,
  Users,
  TrendingUp,
  Calendar,
  Bell,
  BarChart3,
  Zap,
  CheckCircle,
  Play,
  Award,
  Globe,
  Smartphone,
  Laptop,
} from "lucide-react";
import styles from "@/styles/patterns.module.css";
import { useEffect } from "react";
import { isAuthDataCorrupted, clearAllAuthData } from "@/utils/auth-cleanup";

export default function LandingPage() {
  // Clean up any corrupted authentication data when landing page loads
  useEffect(() => {
    if (isAuthDataCorrupted()) {
      console.log('Detected corrupted authentication data, cleaning up...');
      clearAllAuthData();
    }
  }, []);

  return (
    <div className="flex flex-col min-h-screen bg-gradient-to-br from-slate-50 via-white to-blue-50">
      {/* Hero Section - Full Screen */}
      <section className="relative h-screen flex items-center justify-center overflow-hidden">
        {/* Animated Background */}
        <div className="absolute inset-0 bg-gradient-to-br from-blue-600/10 via-purple-600/5 to-pink-600/10" />
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_30%_40%,rgba(120,119,198,0.3),transparent_50%)]" />
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_70%_80%,rgba(255,105,180,0.2),transparent_50%)]" />

        {/* Floating Elements */}
        <div className="absolute top-1/4 left-1/4 w-64 h-64 bg-gradient-to-r from-blue-400/20 to-purple-400/20 rounded-full blur-3xl animate-pulse" />
        <div className="absolute bottom-1/4 right-1/4 w-96 h-96 bg-gradient-to-r from-pink-400/20 to-orange-400/20 rounded-full blur-3xl animate-pulse delay-1000" />

        <div className="container mx-auto px-4 text-center relative z-10">
          <div className="max-w-5xl mx-auto">
            {/* Badge */}
            <div className="inline-flex items-center gap-2 bg-gradient-to-r from-blue-500/10 to-purple-500/10 backdrop-blur-sm border border-blue-200/50 rounded-full px-6 py-2 mb-8 animate-fade-in">
              <Star className="h-4 w-4 text-yellow-500 fill-yellow-500" />
              <span className="text-sm font-medium text-gray-700">
                Trusted by 50,000+ professionals worldwide
              </span>
            </div>

            {/* Main Heading */}
            <h1 className="text-5xl md:text-7xl lg:text-8xl font-bold mb-8 leading-tight">
              <span className="bg-clip-text text-transparent bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 animate-gradient-x">
                Track Your Time
              </span>
              <br />
              <span className="bg-clip-text text-transparent bg-gradient-to-r from-pink-600 via-red-500 to-orange-500 animate-gradient-x">
                Transform Your Life
              </span>
            </h1>

            {/* Subtitle */}
            <p className="text-xl md:text-2xl lg:text-3xl text-gray-600 mb-12 max-w-4xl mx-auto leading-relaxed">
              The ultimate productivity companion that adapts to your lifestyle.
              <span className="text-blue-600 font-semibold">
                {" "}
                Smart scheduling
              </span>
              ,
              <span className="text-purple-600 font-semibold">
                {" "}
                intelligent insights
              </span>
              - all in one beautiful platform.
            </p>

            {/* CTA Buttons */}
            <div className="flex flex-col sm:flex-row justify-center gap-6 mb-16">
              <Link href="/auth/register">
                <Button
                  size="lg"
                  className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white px-8 py-4 text-lg rounded-xl shadow-xl hover:shadow-2xl transform hover:scale-105 transition-all duration-300 gap-3"
                >
                  Register <ArrowRight className="h-5 w-5" />
                </Button>
              </Link>
              <Link href="/auth/login">
                <Button
                  size="lg"
                  variant="outline"
                  className="border-2  border-blue-500 px-8 py-4 text-lg rounded-xl shadow-lg  transform hover:scale-105 transition-all duration-300 gap-3backdrop-blur-sm"
                >
                  <Play className="h-5 w-5 mr-2" /> Login
                </Button>
              </Link>
            </div>

            {/* Trust Indicators */}
            <div className="flex flex-wrap justify-center items-center gap-8 opacity-90">
              <div className="flex items-center gap-2">
                <Users className="h-5 w-5 text-blue-500" />
                <span className="text-sm font-medium text-black">
                  50k+ Users
                </span>
              </div>
              <div className="flex items-center gap-2">
                <Star className="h-5 w-5 text-yellow-500 fill-yellow-500" />
                <span className="text-sm font-medium text-black">
                  4.9/5 Rating
                </span>
              </div>
              <div className="flex items-center gap-2">
                <Shield className="h-5 w-5 text-green-500" />
                <span className="text-sm font-medium text-black">
                  Enterprise Secure
                </span>
              </div>
            </div>
          </div>
        </div>

        {/* Scroll Indicator */}
        <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
          <div className="w-6 h-10 border-2 border-gray-400 rounded-full flex justify-center">
            <div className="w-1 h-3 bg-gray-400 rounded-full mt-2 animate-pulse"></div>
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-20 bg-gradient-to-r from-blue-600 to-purple-600 relative overflow-hidden">
        <div className={`absolute inset-0 ${styles.dotsPattern} opacity-20`} />
        <div className="container mx-auto px-4">
          <div className="grid md:grid-cols-4 gap-8 text-center text-white">
            <div className="group hover:scale-110 transition-transform duration-300">
              <div className="text-5xl font-bold mb-2 bg-gradient-to-r from-yellow-300 to-orange-300 bg-clip-text text-transparent">
                50k+
              </div>
              <p className="text-blue-100 text-lg">Active Users</p>
              <div className="h-1 w-16 bg-gradient-to-r from-yellow-300 to-orange-300 mx-auto mt-2 rounded-full"></div>
            </div>
            <div className="group hover:scale-110 transition-transform duration-300">
              <div className="text-5xl font-bold mb-2 bg-gradient-to-r from-green-300 to-teal-300 bg-clip-text text-transparent">
                2.5M+
              </div>
              <p className="text-blue-100 text-lg">Tasks Completed</p>
              <div className="h-1 w-16 bg-gradient-to-r from-green-300 to-teal-300 mx-auto mt-2 rounded-full"></div>
            </div>
            <div className="group hover:scale-110 transition-transform duration-300">
              <div className="text-5xl font-bold mb-2 bg-gradient-to-r from-pink-300 to-rose-300 bg-clip-text text-transparent">
                99.2%
              </div>
              <p className="text-blue-100 text-lg">Satisfaction Rate</p>
              <div className="h-1 w-16 bg-gradient-to-r from-pink-300 to-rose-300 mx-auto mt-2 rounded-full"></div>
            </div>
            <div className="group hover:scale-110 transition-transform duration-300">
              <div className="text-5xl font-bold mb-2 bg-gradient-to-r from-purple-300 to-indigo-300 bg-clip-text text-transparent">
                45%
              </div>
              <p className="text-blue-100 text-lg">Productivity Boost</p>
              <div className="h-1 w-16 bg-gradient-to-r from-purple-300 to-indigo-300 mx-auto mt-2 rounded-full"></div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-24 bg-gradient-to-b from-white to-gray-50 relative overflow-hidden">
        <div className="absolute inset-0 bg-[radial-gradient(circle_at_50%_50%,rgba(59,130,246,0.05),transparent_70%)]" />
        <div className="container mx-auto px-4">
          <div className="text-center max-w-4xl mx-auto mb-20">
            <h2 className="text-4xl md:text-5xl font-bold mb-6 bg-clip-text text-transparent bg-gradient-to-r from-blue-600 to-purple-600">
              Powerful Features, Seamless Experience
            </h2>
            <p className="text-xl text-gray-600 leading-relaxed">
              Discover how NoteHour intelligent features work together to create
              the perfect productivity ecosystem tailored to your unique
              workflow.
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
            {[
              {
                icon: Clock,
                title: "Smart Time Blocking",
                description:
                  "AI-powered scheduling that learns your patterns and suggests optimal time slots for maximum productivity.",
                color: "from-blue-500 to-blue-600",
              },
              {
                icon: Target,
                title: "Adaptive Routines",
                description:
                  "Flexible routines that evolve with your lifestyle, featuring intelligent adjustments and smart reminders.",
                color: "from-purple-500 to-purple-600",
              },
              {
                icon: BarChart3,
                title: "Advanced Analytics",
                description:
                  "Deep insights into your productivity patterns with beautiful visualizations and actionable recommendations.",
                color: "from-green-500 to-green-600",
              },
              {
                icon: Bell,
                title: "Smart Notifications",
                description:
                  "Context-aware reminders that respect your focus time and adapt to your current activity.",
                color: "from-orange-500 to-orange-600",
              },
              {
                icon: Zap,
                title: "Automation Engine",
                description:
                  "Powerful automation that handles repetitive tasks, freeing you to focus on what matters most.",
                color: "from-pink-500 to-pink-600",
              },
              {
                icon: Calendar,
                title: "Universal Integration",
                description:
                  "Seamlessly sync with Google Calendar, Outlook, Notion, and 50+ other tools you already use.",
                color: "from-teal-500 to-teal-600",
              },
            ].map((feature, index) => (
              <div
                key={index}
                className="group bg-white rounded-2xl p-8 shadow-lg hover:shadow-2xl transition-all duration-500 border border-gray-100 hover:border-gray-200 transform hover:-translate-y-2"
              >
                <div
                  className={`h-16 w-16 rounded-2xl bg-gradient-to-r ${feature.color} flex items-center justify-center mb-6 group-hover:scale-110 transition-transform duration-300`}
                >
                  <feature.icon className="h-8 w-8 text-white" />
                </div>
                <h3 className="text-2xl font-bold mb-4 text-gray-800 group-hover:text-blue-600 transition-colors">
                  {feature.title}
                </h3>
                <p className="text-gray-600 leading-relaxed">
                  {feature.description}
                </p>
              </div>
            ))}
          </div>
        </div>
      </section>
      {/* How It Works Section */}
      <section className="py-24 bg-gradient-to-b from-gray-50 to-white relative">
        <div className="container mx-auto px-4">
          <div className="text-center max-w-4xl mx-auto mb-20">
            <h2 className="text-4xl md:text-5xl font-bold mb-6 bg-clip-text text-transparent bg-gradient-to-r from-purple-600 to-pink-600">
              How NoteHour Works
            </h2>
            <p className="text-xl text-gray-600">
              Get started in minutes with our intuitive three-step process
            </p>
          </div>

          <div className="grid md:grid-cols-3 gap-12 relative">
            {/* Connection Lines */}
            <div className="hidden md:block absolute top-1/2 left-1/3 right-1/3 h-0.5 bg-gradient-to-r from-purple-300 to-pink-300 transform -translate-y-1/2"></div>

            {[
              {
                step: "01",
                title: "Connect & Setup",
                description:
                  "Link your existing calendars and tools. Our AI analyzes your patterns to create a personalized productivity profile.",
                icon: Globe,
              },
              {
                step: "02",
                title: "Plan & Schedule",
                description:
                  "Create smart routines and time blocks. Our intelligent suggestions help optimize your daily schedule automatically.",
                icon: Calendar,
              },
              {
                step: "03",
                title: "Track & Improve",
                description:
                  "Monitor your progress with detailed analytics. Receive personalized insights to continuously improve your productivity.",
                icon: TrendingUp,
              },
            ].map((step, index) => (
              <div key={index} className="text-center relative">
                <div className="bg-gradient-to-r from-purple-500 to-pink-500 text-white rounded-full w-20 h-20 flex items-center justify-center mx-auto mb-6 text-2xl font-bold shadow-xl">
                  {step.step}
                </div>
                <div className="bg-white rounded-xl p-6 shadow-lg border border-gray-100 hover:shadow-xl transition-shadow duration-300">
                  <step.icon className="h-12 w-12 text-purple-500 mx-auto mb-4" />
                  <h3 className="text-2xl font-bold mb-4 text-gray-800">
                    {step.title}
                  </h3>
                  <p className="text-gray-600 leading-relaxed">
                    {step.description}
                  </p>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Testimonials Section */}
      <section className="py-24 bg-gradient-to-r from-blue-50 to-purple-50 relative overflow-hidden">
        <div
          className={`absolute inset-0 ${styles.circlesPattern} opacity-50`}
        />
        <div className="container mx-auto px-4">
          <div className="text-center max-w-4xl mx-auto mb-20">
            <h2 className="text-4xl md:text-5xl font-bold mb-6 bg-clip-text text-transparent bg-gradient-to-r from-blue-600 to-purple-600">
              Loved by Professionals Worldwide
            </h2>
            <p className="text-xl text-gray-600">
              See how NoteHour is transforming productivity across industries
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[
              {
                name: "Sarah Chen",
                role: "Product Manager at Google",
                avatar: "SC",
                rating: 5,
                testimonial:
                  "NoteHour completely transformed how I manage my product roadmap. The intelligent scheduling suggestions are incredibly accurate, and I've increased my team's productivity by 40%.",
              },
              {
                name: "Marcus Rodriguez",
                role: "Freelance Designer",
                avatar: "MR",
                rating: 5,
                testimonial:
                  "As a freelancer juggling multiple clients, NoteHour's time tracking and automated invoicing features have saved me 10+ hours per week. It's like having a personal assistant.",
              },
              {
                name: "Dr. Emily Watson",
                role: "Medical Researcher",
                avatar: "EW",
                rating: 5,
                testimonial:
                  "The analytics dashboard helps me visualize my research patterns and optimize my lab time. I've published 3 more papers this year thanks to better time management.",
              },
              {
                name: "Alex Thompson",
                role: "Software Engineer",
                avatar: "AT",
                rating: 5,
                testimonial:
                  "The deep work scheduling feature is a game-changer. NoteHour blocks my coding time perfectly and the distraction blocking keeps me in the zone for hours.",
              },
              {
                name: "Lisa Park",
                role: "MBA Student",
                avatar: "LP",
                rating: 5,
                testimonial:
                  "Balancing studies, internship, and personal life seemed impossible until I found NoteHour. The adaptive routines feature is brilliant for my changing schedule.",
              },
              {
                name: "James Wilson",
                role: "Marketing Director",
                avatar: "JW",
                rating: 5,
                testimonial:
                  "Our entire marketing team uses NoteHour now. The collaboration features and shared analytics have improved our campaign delivery times by 25%.",
              },
            ].map((testimonial, index) => (
              <div
                key={index}
                className="bg-white/80 backdrop-blur-sm p-8 rounded-2xl shadow-lg border border-white/50 hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-1"
              >
                <div className="flex items-center mb-6">
                  <div className="h-14 w-14 rounded-full bg-gradient-to-r from-blue-500 to-purple-500 flex items-center justify-center mr-4 text-white font-bold text-lg">
                    {testimonial.avatar}
                  </div>
                  <div>
                    <h4 className="font-bold text-lg text-gray-800">
                      {testimonial.name}
                    </h4>
                    <p className="text-sm text-gray-600">{testimonial.role}</p>
                    <div className="flex mt-1">
                      {[...Array(testimonial.rating)].map((_, i) => (
                        <Star
                          key={i}
                          className="h-4 w-4 text-yellow-400 fill-yellow-400"
                        />
                      ))}
                    </div>
                  </div>
                </div>
                <p className="text-gray-700 leading-relaxed italic">
                  &ldquo;{testimonial.testimonial}&rdquo;
                </p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Platform Compatibility */}
      <section className="py-20 bg-gradient-to-r from-gray-100 to-gray-50">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl font-bold mb-12 text-gray-800">
            Works Everywhere You Do
          </h2>
          <div className="flex justify-center items-center gap-12 flex-wrap">
            <div className="flex flex-col items-center group">
              <Smartphone className="h-12 w-12 text-blue-500 mb-2 group-hover:scale-110 transition-transform" />
              <span className="text-gray-600">Mobile</span>
            </div>
            <div className="flex flex-col items-center group">
              <Laptop className="h-12 w-12 text-purple-500 mb-2 group-hover:scale-110 transition-transform" />
              <span className="text-gray-600">Desktop</span>
            </div>
            <div className="flex flex-col items-center group">
              <Globe className="h-12 w-12 text-green-500 mb-2 group-hover:scale-110 transition-transform" />
              <span className="text-gray-600">Web</span>
            </div>
          </div>
        </div>
      </section>

      {/* Final CTA Section */}
      <section className="py-24 bg-gradient-to-r from-blue-600 via-purple-600 to-pink-600 relative overflow-hidden">
        <div className="absolute inset-0 bg-black/20" />
        <div className={`absolute inset-0 ${styles.dotsPattern}`} />
        <div className="container mx-auto px-4 text-center relative z-10">
          <div className="max-w-4xl mx-auto">
            <Award className="h-16 w-16 text-yellow-300 mx-auto mb-6" />
            <h2 className="text-4xl md:text-5xl font-bold mb-6 text-white">
              Join the Productivity Revolution
            </h2>
            <p className="text-xl md:text-2xl text-blue-100 mb-10 leading-relaxed">
              Over 50,000 professionals have already transformed their
              productivity with NoteHour.
              <br className="hidden md:block" />
              Your most productive self is just one click away.
            </p>
            <div className="flex flex-col sm:flex-row justify-center gap-6">
              <Link href="/auth/register">
                <Button
                  size="lg"
                  className="bg-white text-blue-600 hover:bg-gray-100 px-10 py-4 text-lg rounded-xl shadow-xl hover:shadow-2xl transform hover:scale-105 transition-all duration-300 gap-3 font-semibold"
                >
                  Start Free
                  <ArrowRight className="h-5 w-5" />
                </Button>
              </Link>
              <Link href="/auth/login">
                <Button
                  size="lg"
                  variant="outline"
                  className="border-2 border-white text-white hover:bg-white hover:text-blue-600 px-10 py-4 text-lg rounded-xl shadow-xl hover:shadow-2xl transform hover:scale-105 transition-all duration-300 font-semibold"
                >
                  Sign In
                </Button>
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-16">
        <div className="container mx-auto px-4">
          <div className="grid md:grid-cols-4 gap-8 mb-12">
            <div>
              <Link
                href="/"
                className="text-3xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-blue-400 to-purple-400 mb-4 block"
              >
                NoteHour
              </Link>
              <p className="text-gray-400 leading-relaxed mb-4">
                The ultimate productivity companion that transforms how you
                manage time and achieve your goals.
              </p>
              <div className="flex gap-4">
                <div className="w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center hover:bg-blue-700 transition-colors cursor-pointer">
                  <span className="text-sm font-bold">f</span>
                </div>
                <div className="w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center hover:bg-blue-600 transition-colors cursor-pointer">
                  <span className="text-sm font-bold">t</span>
                </div>
                <div className="w-10 h-10 bg-blue-700 rounded-full flex items-center justify-center hover:bg-blue-800 transition-colors cursor-pointer">
                  <span className="text-sm font-bold">in</span>
                </div>
              </div>
            </div>

            <div>
              <h4 className="text-lg font-semibold mb-4">Product</h4>
              <ul className="space-y-2">
                <li>
                  <a
                    href="#"
                    className="text-gray-400 hover:text-white transition-colors"
                  >
                    Features
                  </a>
                </li>
                <li>
                  <a
                    href="#"
                    className="text-gray-400 hover:text-white transition-colors"
                  >
                    Pricing
                  </a>
                </li>
                <li>
                  <a
                    href="#"
                    className="text-gray-400 hover:text-white transition-colors"
                  >
                    Integrations
                  </a>
                </li>
                <li>
                  <a
                    href="#"
                    className="text-gray-400 hover:text-white transition-colors"
                  >
                    API
                  </a>
                </li>
                <li>
                  <a
                    href="#"
                    className="text-gray-400 hover:text-white transition-colors"
                  >
                    Changelog
                  </a>
                </li>
              </ul>
            </div>

            <div>
              <h4 className="text-lg font-semibold mb-4">Company</h4>
              <ul className="space-y-2">
                <li>
                  <a
                    href="#"
                    className="text-gray-400 hover:text-white transition-colors"
                  >
                    About Us
                  </a>
                </li>
                <li>
                  <a
                    href="#"
                    className="text-gray-400 hover:text-white transition-colors"
                  >
                    Careers
                  </a>
                </li>
                <li>
                  <a
                    href="#"
                    className="text-gray-400 hover:text-white transition-colors"
                  >
                    Blog
                  </a>
                </li>
                <li>
                  <a
                    href="#"
                    className="text-gray-400 hover:text-white transition-colors"
                  >
                    Press
                  </a>
                </li>
                <li>
                  <a
                    href="#"
                    className="text-gray-400 hover:text-white transition-colors"
                  >
                    Contact
                  </a>
                </li>
              </ul>
            </div>

            <div>
              <h4 className="text-lg font-semibold mb-4">Support</h4>
              <ul className="space-y-2">
                <li>
                  <a
                    href="#"
                    className="text-gray-400 hover:text-white transition-colors"
                  >
                    Help Center
                  </a>
                </li>
                <li>
                  <a
                    href="#"
                    className="text-gray-400 hover:text-white transition-colors"
                  >
                    Documentation
                  </a>
                </li>
                <li>
                  <a
                    href="#"
                    className="text-gray-400 hover:text-white transition-colors"
                  >
                    Community
                  </a>
                </li>
                <li>
                  <a
                    href="#"
                    className="text-gray-400 hover:text-white transition-colors"
                  >
                    Status
                  </a>
                </li>
                <li>
                  <Link
                    href="/auth/login"
                    className="text-gray-400 hover:text-white transition-colors"
                  >
                    Login
                  </Link>
                </li>
              </ul>
            </div>
          </div>

          <div className="border-t border-gray-800 pt-8 flex flex-col md:flex-row justify-between items-center">
            <div className="mb-4 md:mb-0">
              <p className="text-gray-400 text-sm">
                © {new Date().getFullYear()} NoteHour. All rights reserved.
              </p>
            </div>
            <div className="flex gap-6 text-sm">
              <a
                href="#"
                className="text-gray-400 hover:text-white transition-colors"
              >
                Privacy Policy
              </a>
              <a
                href="#"
                className="text-gray-400 hover:text-white transition-colors"
              >
                Terms of Service
              </a>
              <a
                href="#"
                className="text-gray-400 hover:text-white transition-colors"
              >
                Cookie Policy
              </a>
            </div>
          </div>
        </div>
      </footer>
    </div>
  );
}
