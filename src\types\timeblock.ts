/**
 * TimeBlock interface
 */
export interface TimeBlock {
  id: string;
  userId: string;
  date: string;
  startTime: string;
  endTime: string;
  note: string;
  category: string; // This will be the category ID
  categoryData?: {
    name: string;
    color: string;
  };
  isTodo: boolean;
  isCompleted: boolean;
  routineId?: string;
  showInMyTodos?: boolean; // For filtering routine tasks in MyTodos view
  isToday?: boolean; // Flag for tasks scheduled for today
  createdAt: string;
  updatedAt: string;
}

/**
 * TimeBlock form data interface (used in frontend forms)
 * This matches the Omit<TimeBlock, 'id' | 'userId'> type expected by addTimeBlock
 */
export interface TimeBlockFormData {
  date: string;
  startTime: string;
  endTime: string;
  note: string;
  category: string; // This will be the category ID
  isTodo?: boolean;
  isCompleted?: boolean;
  routineId?: string;
  createdAt?: string;
  updatedAt?: string;
}

/**
 * Create TimeBlock request interface
 */
export interface CreateTimeBlockRequest {
  date: string;
  startTime: string;
  endTime: string;
  note: string;
  category: string; // This will be the category ID
  isTodo?: boolean;
  isCompleted?: boolean;
  routineId?: string;
}

/**
 * Update TimeBlock request interface
 */
export interface UpdateTimeBlockRequest {
  date?: string;
  startTime?: string;
  endTime?: string;
  note?: string;
  category?: string; // This will be the category ID
  isTodo?: boolean;
  isCompleted?: boolean;
  routineId?: string;
}
