"use client"

import { useState } from 'react';
import Link from 'next/link';
import { useRouter } from 'next/navigation';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { AlertCircle, LayoutDashboard, Loader2 } from 'lucide-react';
import { toast } from 'sonner';
import { Alert, AlertDescription } from '@/components/ui/alert';

const keyAuthSchema = z.object({
  accessKey: z.string()
    .min(1, { message: 'Access key is required' })
    .refine((key) => {
      // New format: 8 words + 10 digits
      const newFormat = /^[a-z]+-[a-z]+-[a-z]+-[a-z]+-[a-z]+-[a-z]+-[a-z]+-[a-z]+-\d{10}$/.test(key);
      // Legacy format: 2 words + 2 digits
      const legacyFormat = /^[a-z]+-[a-z]+-\d{2}$/.test(key);
      return newFormat || legacyFormat;
    }, 'Invalid access key format. Expected: 8-words-10digits or legacy 2-words-2digits'),
});

const recoveryCodeAuthSchema = z.object({
  accessKey: z.string()
    .min(1, { message: 'Access key is required' })
    .refine((key) => {
      const newFormat = /^[a-z]+-[a-z]+-[a-z]+-[a-z]+-[a-z]+-[a-z]+-[a-z]+-[a-z]+-\d{10}$/.test(key);
      const legacyFormat = /^[a-z]+-[a-z]+-\d{2}$/.test(key);
      return newFormat || legacyFormat;
    }, 'Invalid access key format'),
  recoveryCode: z.string()
    .min(1, { message: 'Recovery code is required' })
    .regex(/^[A-F0-9]{8}$/, { message: 'Invalid recovery code format. Expected: 8 uppercase alphanumeric characters' }),
});

export default function LoginPage() {
  const [isLoading, setIsLoading] = useState(false);
  const [errorDetails, setErrorDetails] = useState<string | null>(null);
  const [useRecoveryCode, setUseRecoveryCode] = useState(false);
  const router = useRouter();

  const currentSchema = useRecoveryCode ? recoveryCodeAuthSchema : keyAuthSchema;

  const form = useForm<z.infer<typeof keyAuthSchema> | z.infer<typeof recoveryCodeAuthSchema>>({
    resolver: zodResolver(currentSchema),
    defaultValues: {
      accessKey: '',
      ...(useRecoveryCode && { recoveryCode: '' }),
    },
  });



  async function onSubmit(values: z.infer<typeof keyAuthSchema> | z.infer<typeof recoveryCodeAuthSchema>) {
    setIsLoading(true);
    setErrorDetails(null);

    try {
      // Prepare request body based on authentication type
      const requestBody: any = {
        accessKey: values.accessKey.toLowerCase().trim()
      };

      if (useRecoveryCode && 'recoveryCode' in values) {
        requestBody.recoveryCode = values.recoveryCode.toUpperCase().trim();
      }

      // Make the authentication request
      const response = await fetch('/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(requestBody),
        credentials: 'include',
      });

      // Get the response text
      const responseText = await response.text();

      // Parse the JSON response
      let data;
      try {
        data = JSON.parse(responseText);
      } catch (e) {
        setErrorDetails('Invalid response from server. Please try again later.');
        throw new Error('Server returned an invalid response');
      }

      // Check if login was successful
      if (!response.ok || !data.success) {
        const errorMessage = data.error?.message || 'Unknown error';
        const errorCode = data.error?.code || 'UNKNOWN_ERROR';

        // Provide user-friendly error messages
        if (errorCode === 'INVALID_CREDENTIALS' || errorCode === 'USER_NOT_FOUND') {
          setErrorDetails('Invalid access key. Please check your key and try again.');
          throw new Error('Invalid access key');
        } else if (errorCode === 'DATABASE_ERROR') {
          setErrorDetails('Database connection error. Please try again later.');
          throw new Error('Database connection failed');
        } else {
          setErrorDetails(`Authentication failed: ${errorMessage}`);
          throw new Error(errorMessage || 'Authentication failed');
        }
      }

      // Extract user data and token
      const responseData = data.data || {};
      const token = responseData.token;
      const user = responseData.user;

      if (!token || !user) {
        setErrorDetails('Server response is missing required data');
        throw new Error('Invalid server response');
      }

      // Store in localStorage for PWA support and offline access
      localStorage.setItem('token', token);
      localStorage.setItem('user', JSON.stringify({
        id: user.id,
        name: user.name,
        email: user.email,
        lastLogin: new Date().toISOString()
      }));

      // Also store the complete profile
      localStorage.setItem('userProfile', JSON.stringify(user));

      // --- Fix: Clear categories cache/localStorage so next page fetches fresh categories ---
      localStorage.removeItem('categories');
      // --------------------------------------------------------------

      toast.success('Logged in successfully');
      router.push('/home');
    } catch (error: any) {
      toast.error(error.message || 'Login failed');
    } finally {
      setIsLoading(false);
    }
  }

  return (
    <div className="flex min-h-screen flex-col items-center justify-center p-4">
      <Link href="/" className="absolute left-4 top-4 md:left-8 md:top-8 flex items-center gap-2 font-bold">
        <LayoutDashboard className="h-5 w-5" />
        <span>NoteHour</span>
      </Link>

      <Card className="w-full max-w-md">
        <CardHeader>
          <CardTitle className="text-2xl">Access Your Account</CardTitle>
          <CardDescription>
            {useRecoveryCode
              ? 'Enter your access key and recovery code to sign in'
              : 'Enter your access key to sign in'
            }
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
              {errorDetails && (
                <Alert variant="destructive" className="mb-4">
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription className="mt-2">
                    <div className="text-sm font-medium">Error Details:</div>
                    <div className="text-xs mt-1 break-words">{errorDetails}</div>
                  </AlertDescription>
                </Alert>
              )}

              <FormField
                control={form.control}
                name="accessKey"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Access Key</FormLabel>
                    <FormControl>
                      <Input
                        placeholder={useRecoveryCode ? "your-access-key" : "swift-river-ancient-brave-clever-daring-eager-fancy-1234567890"}
                        {...field}
                        className="font-mono"
                        autoComplete="off"
                      />
                    </FormControl>
                    <FormMessage />
                    <div className="text-xs text-muted-foreground mt-1">
                      {useRecoveryCode
                        ? 'Enter the access key associated with your recovery code'
                        : 'Format: 8-words-10digits (new) or word-word-number (legacy)'
                      }
                    </div>
                  </FormItem>
                )}
              />

              {useRecoveryCode && (
                <FormField
                  control={form.control}
                  name="recoveryCode"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Recovery Code</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="A1B2C3D4"
                          {...field}
                          className="font-mono"
                          autoComplete="off"
                          maxLength={8}
                        />
                      </FormControl>
                      <FormMessage />
                      <div className="text-xs text-muted-foreground mt-1">
                        Enter one of your 8-character recovery codes
                      </div>
                    </FormItem>
                  )}
                />
              )}

              <Button type="submit" className="w-full" disabled={isLoading}>
                {isLoading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Please wait
                  </>
                ) : (
                  useRecoveryCode ? 'Sign In with Recovery Code' : 'Sign In'
                )}
              </Button>

              <div className="flex justify-center">
                <Button
                  type="button"
                  variant="link"
                  size="sm"
                  onClick={() => {
                    setUseRecoveryCode(!useRecoveryCode);
                    form.reset();
                    setErrorDetails(null);
                  }}
                  className="text-xs"
                >
                  {useRecoveryCode
                    ? 'Use access key instead'
                    : 'Lost access? Use recovery code'
                  }
                </Button>
              </div>

              {!useRecoveryCode && (
                <div className="text-xs text-center text-muted-foreground mt-2">
                  🔐 Enhanced security: Now supports stronger access keys and recovery codes
                </div>
              )}
            </form>
          </Form>
        </CardContent>
        <CardFooter className="flex flex-col items-center gap-2">
          <div className="text-sm text-muted-foreground">
            Need an access key?{' '}
            <Link href="/auth/register" className="font-medium text-primary underline-offset-4 hover:underline">
              Generate Key
            </Link>
          </div>


        </CardFooter>
      </Card>
    </div>
  );
}
