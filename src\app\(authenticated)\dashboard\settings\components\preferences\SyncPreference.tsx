"use client"

import { FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Switch } from '@/components/ui/switch';
import { UseFormReturn } from 'react-hook-form';
import { PreferencesFormValues } from '../../schemas';

interface SyncPreferenceProps {
  form: UseFormReturn<PreferencesFormValues>;
}

export function SyncPreference({ form }: SyncPreferenceProps) {
  return (
    <div className="bg-muted/10 p-3 rounded-md">
      <FormField
        control={form.control}
        name="syncEnabled"
        render={({ field }) => (
          <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3">
            <div className="space-y-0.5">
              <FormLabel className="text-sm font-medium">Tab Refresh Sync</FormLabel>
              <FormDescription className="text-xs">
                Refresh data when returning to this tab (no automatic reloading while using the app)
              </FormDescription>
            </div>
            <FormControl>
              <Switch
                checked={field.value}
                onCheckedChange={field.onChange}
              />
            </FormControl>
            <FormMessage />
          </FormItem>
        )}
      />
    </div>
  );
}
