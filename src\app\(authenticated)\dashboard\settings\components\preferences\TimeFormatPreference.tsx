"use client"

import { FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { UseFormReturn } from 'react-hook-form';
import { PreferencesFormValues } from '../../schemas';

interface TimeFormatPreferenceProps {
  form: UseFormReturn<PreferencesFormValues>;
}

export function TimeFormatPreference({ form }: TimeFormatPreferenceProps) {
  return (
    <div className="bg-muted/10 p-3 rounded-md">
      <FormField
        control={form.control}
        name="timeFormat"
        render={({ field }) => (
          <FormItem>
            <FormLabel className="text-sm font-medium">Time Format</FormLabel>
            <FormControl>
              <RadioGroup
                onValueChange={field.onChange}
                defaultValue={field.value}
                className="flex flex-wrap gap-2 mt-1"
              >
                <div className="flex items-center space-x-1 border rounded-md px-2 py-1 bg-card hover:bg-muted/30 transition-colors">
                  <RadioGroupItem value="12" id="12-hour" />
                  <FormLabel htmlFor="12-hour" className="font-normal cursor-pointer text-sm">12-hour (AM/PM)</FormLabel>
                </div>
                <div className="flex items-center space-x-1 border rounded-md px-2 py-1 bg-card hover:bg-muted/30 transition-colors">
                  <RadioGroupItem value="24" id="24-hour" />
                  <FormLabel htmlFor="24-hour" className="font-normal cursor-pointer text-sm">24-hour</FormLabel>
                </div>
              </RadioGroup>
            </FormControl>
            <FormDescription className="mt-1 text-xs">
              Choose how time is displayed throughout the app
            </FormDescription>
            <FormMessage />
          </FormItem>
        )}
      />
    </div>
  );
}
