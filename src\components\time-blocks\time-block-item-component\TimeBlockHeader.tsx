"use client"

import { Clock } from 'lucide-react';
import { TimeBlock } from '@/lib/types';
import { usePreferences } from '@/hooks/use-preferences';

interface TimeBlockHeaderProps {
  timeBlock: TimeBlock;
}

export function TimeBlockHeader({ timeBlock }: TimeBlockHeaderProps) {
  const { formatTime } = usePreferences();

  // Calculate duration in minutes
  const getDuration = () => {
    const startTime = timeBlock.startTime.split(':').map(Number);
    const endTime = timeBlock.endTime.split(':').map(Number);

    const startMinutes = startTime[0] * 60 + startTime[1];
    const endMinutes = endTime[0] * 60 + endTime[1];

    const durationMinutes = endMinutes - startMinutes;
    const hours = Math.floor(durationMinutes / 60);
    const minutes = durationMinutes % 60;

    if (hours > 0) {
      return `${hours}h ${minutes > 0 ? `${minutes}m` : ''}`;
    }
    return `${minutes}m`;
  };

  return (
    <div className="flex justify-center items-center mb-1">
      <div className="flex items-center gap-2 text-sm text-muted-foreground">
        <Clock className="h-4 w-4" />
        <span className="font-medium">
          {formatTime(timeBlock.startTime)} - {formatTime(timeBlock.endTime)}
        </span>
        <span className="font-medium">({getDuration()})</span>
      </div>
    </div>
  );
}
