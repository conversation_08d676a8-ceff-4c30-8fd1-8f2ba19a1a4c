"use client"

import { format } from 'date-fns';
import { Calendar as CalendarIcon, ChevronLeft, ChevronRight, Plus } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';

interface DateNavigationProps {
  selectedDate: Date;
  setSelectedDate: (date: Date) => void;
  navigateWeek: (direction: 'prev' | 'next') => void;
  onAddTimeBlock: () => void;
}

export function DateNavigation({ 
  selectedDate, 
  setSelectedDate, 
  navigateWeek, 
  onAddTimeBlock 
}: DateNavigationProps) {
  return (
    <div className="flex items-center justify-between gap-2">
      {/* Left side: Date and navigation */}
      <div className="flex items-center gap-2">
        <div className="flex items-center gap-1">
          <Button 
            variant="ghost" 
            size="icon" 
            className="h-8 w-8" 
            onClick={() => navigateWeek('prev')}
          >
            <ChevronLeft className="h-4 w-4" />
          </Button>
          <Button 
            variant="ghost" 
            size="icon" 
            className="h-8 w-8" 
            onClick={() => navigateWeek('next')}
          >
            <ChevronRight className="h-4 w-4" />
          </Button>
        </div>
        <div className="flex items-center gap-2">
          <p className="text-sm font-medium">
            {format(selectedDate, 'EEE, MMM d')}
          </p>
          <Popover>
            <PopoverTrigger asChild>
              <Button variant="ghost" size="icon" className="h-8 w-8">
                <CalendarIcon className="h-4 w-4" />
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0" align="start">
              <Calendar
                mode="single"
                selected={selectedDate}
                onSelect={(date) => date && setSelectedDate(date)}
                initialFocus
              />
            </PopoverContent>
          </Popover>
        </div>
      </div>

      {/* Right side: Add button */}
      <Button
        onClick={onAddTimeBlock}
        size="sm"
        className="h-8 px-3"
      >
        <Plus className="h-4 w-4 mr-1.5" />
        <span className="text-sm">Add</span>
      </Button>
    </div>
  );
}
