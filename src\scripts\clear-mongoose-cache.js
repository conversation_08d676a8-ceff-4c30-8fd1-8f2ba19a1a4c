/**
 * Clear Mongoose model cache and force schema recreation
 * This script clears all Mongoose caches and recreates the User model
 * Run with: node src/scripts/clear-mongoose-cache.js
 */

const mongoose = require('mongoose');
const path = require('path');
const fs = require('fs');

// Load environment variables from .env file
const envPath = path.join(__dirname, '../../.env');
if (fs.existsSync(envPath)) {
  const envContent = fs.readFileSync(envPath, 'utf8');
  const envLines = envContent.split('\n');
  envLines.forEach(line => {
    const equalIndex = line.indexOf('=');
    if (equalIndex > 0) {
      const key = line.substring(0, equalIndex).trim();
      const value = line.substring(equalIndex + 1).trim();
      if (key && value) {
        process.env[key] = value;
      }
    }
  });
}

const MONGODB_URI = process.env.MONGODB_URI;

async function clearMongooseCache() {
  try {
    console.log('🧹 Clearing Mongoose model cache...');
    
    // Clear all cached models
    if (mongoose.models && Object.keys(mongoose.models).length > 0) {
      Object.keys(mongoose.models).forEach(modelName => {
        delete mongoose.models[modelName];
        console.log(`Cleared model: ${modelName}`);
      });
    } else {
      console.log('No cached models found');
    }

    // Clear all cached schemas (if they exist)
    if (mongoose.modelSchemas && Object.keys(mongoose.modelSchemas).length > 0) {
      Object.keys(mongoose.modelSchemas).forEach(schemaName => {
        delete mongoose.modelSchemas[schemaName];
        console.log(`Cleared schema: ${schemaName}`);
      });
    } else {
      console.log('No cached schemas found');
    }
    
    console.log('✅ Mongoose cache cleared');
    
    console.log('\n🔗 Connecting to MongoDB...');
    await mongoose.connect(MONGODB_URI);
    console.log('Connected to MongoDB successfully');
    
    const db = mongoose.connection.db;
    const collection = db.collection('users');
    
    // Check current indexes
    console.log('\n📋 Current indexes on users collection:');
    const indexes = await collection.indexes();
    indexes.forEach((index, i) => {
      console.log(`${i + 1}. ${index.name}: ${JSON.stringify(index.key)}`);
    });
    
    // Drop any email indexes if they exist
    const emailIndexes = indexes.filter(index => index.key && index.key.email !== undefined);
    if (emailIndexes.length > 0) {
      console.log('\n🔧 Found email indexes, dropping them...');
      for (const index of emailIndexes) {
        try {
          await collection.dropIndex(index.name);
          console.log(`✅ Dropped index: ${index.name}`);
        } catch (error) {
          console.log(`❌ Failed to drop index ${index.name}:`, error.message);
        }
      }
    }
    
    // Now recreate the User model with the correct schema
    console.log('\n🏗️  Recreating User model with correct schema...');
    
    const userSchema = new mongoose.Schema({
      accessKey: {
        type: String,
        required: [true, 'Access key is required'],
        unique: true,
        trim: true,
        lowercase: true,
        index: true,
      },
      name: {
        type: String,
        trim: true,
        maxlength: [50, 'Name cannot be longer than 50 characters'],
        default: '',
      },
      preferences: {
        timeInterval: {
          type: String,
          enum: ['15', '30', '60', '120', '180', '240', '300', '360', '420', '480', '540', '600', '660', '720'],
          default: '60',
        },
        startHour: {
          type: String,
          default: '0',
        },
        endHour: {
          type: String,
          default: '23',
        },
        timeFormat: {
          type: String,
          enum: ['12', '24'],
          default: '12',
        },
        darkMode: {
          type: Boolean,
          default: false,
        },
        syncEnabled: {
          type: Boolean,
          default: true,
        },
        emailNotifications: {
          type: Boolean,
          default: true,
        },
        customTimeBlocks: [{
          startTime: {
            type: String,
            required: true,
            match: [/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/, 'Start time must be in format HH:MM'],
          },
          endTime: {
            type: String,
            required: true,
            match: [/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/, 'End time must be in format HH:MM'],
          }
        }],
        useCustomTimeBlocks: {
          type: Boolean,
          default: false,
        },
      },
    }, {
      timestamps: true,
    });
    
    // Create the model
    const User = mongoose.model('User', userSchema);
    console.log('✅ User model recreated successfully');
    
    // Test user creation
    console.log('\n🧪 Testing user creation with new model...');
    try {
      const testUser = new User({
        accessKey: 'test-cache-clear-' + Date.now(),
        name: 'Test User',
        preferences: {
          timeInterval: '60',
          startHour: '0',
          endHour: '23',
          timeFormat: '12',
          darkMode: false,
          syncEnabled: true,
          emailNotifications: true,
          useCustomTimeBlocks: false,
          customTimeBlocks: []
        }
      });
      
      const savedUser = await testUser.save();
      console.log('✅ Test user creation successful!');
      
      // Clean up test user
      await User.deleteOne({ _id: savedUser._id });
      console.log('🧹 Test user cleaned up');
      
    } catch (testError) {
      console.log('❌ Test user creation failed:', testError.message);
      if (testError.message.includes('E11000')) {
        console.log('⚠️  Duplicate key error still exists.');
        console.log('Error details:', testError);
      }
    }
    
    // Check final indexes
    console.log('\n📋 Final indexes on users collection:');
    const finalIndexes = await collection.indexes();
    finalIndexes.forEach((index, i) => {
      console.log(`${i + 1}. ${index.name}: ${JSON.stringify(index.key)}`);
    });
    
    console.log('\n✅ Mongoose cache clear completed!');
    
  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    // Close the connection
    if (mongoose.connection.readyState !== 0) {
      await mongoose.disconnect();
      console.log('\nDisconnected from MongoDB');
    }
  }
}

// Run the function
clearMongooseCache();
