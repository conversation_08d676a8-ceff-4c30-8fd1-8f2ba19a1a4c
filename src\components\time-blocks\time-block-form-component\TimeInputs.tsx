"use client"

import { FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { UseFormReturn } from 'react-hook-form';

interface TimeInputsProps {
  form: UseFormReturn<any>;
}

export function TimeInputs({ form }: TimeInputsProps) {
  return (
    <div className="grid grid-cols-2 gap-3">
      <FormField
        control={form.control}
        name="startTime"
        render={({ field }) => (
          <FormItem>
            <FormLabel className="text-sm font-medium">Start Time</FormLabel>
            <FormControl>
              <Input
                type="time"
                className="text-sm h-9 border-input focus-visible:ring-1"
                {...field}
              />
            </FormControl>
            <FormMessage className="text-xs" />
          </FormItem>
        )}
      />
      <FormField
        control={form.control}
        name="endTime"
        render={({ field }) => (
          <FormItem>
            <FormLabel className="text-sm font-medium">End Time</FormLabel>
            <FormControl>
              <Input
                type="time"
                className="text-sm h-9 border-input focus-visible:ring-1"
                {...field}
              />
            </FormControl>
            <FormMessage className="text-xs" />
          </FormItem>
        )}
      />
    </div>
  );
}
