import { AuthController } from './auth.controller';
import { Time<PERSON>lockController } from './timeblock.controller';
import { CategoryController } from './category.controller';

// Export singleton instances
export const authController = new AuthController();
export const timeBlockController = new TimeBlockController();
export const categoryController = new CategoryController();

// Export controller classes
export { AuthController, TimeBlockController, CategoryController };
