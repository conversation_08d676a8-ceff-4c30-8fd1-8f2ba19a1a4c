import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { errorResponse } from '@/utils/response';
import { ErrorCode } from '@/constants/error-codes';

// Time format regex (HH:MM)
const timeRegex = /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/;

// Create time block request schema
export const createTimeBlockSchema = z.object({
  date: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, 'Date must be in format YYYY-MM-DD'),
  startTime: z.string().regex(timeRegex, 'Start time must be in format HH:MM'),
  endTime: z.string().regex(timeRegex, 'End time must be in format HH:MM'),
  note: z.string().min(1, 'Note is required'),
  category: z.string().min(1, 'Category is required'),
  isTodo: z.boolean().optional().default(false),
  isCompleted: z.boolean().optional().default(false),
});

// Update time block request schema
export const updateTimeBlockSchema = z.object({
  date: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, 'Date must be in format YYYY-MM-DD').optional(),
  startTime: z.string().regex(timeRegex, 'Start time must be in format HH:MM').optional(),
  endTime: z.string().regex(timeRegex, 'End time must be in format HH:MM').optional(),
  note: z.string().min(1, 'Note is required').optional(),
  category: z.string().min(1, 'Category is required').optional(),
  isTodo: z.boolean().optional(),
  isCompleted: z.boolean().optional(),
}).refine(data => Object.keys(data).length > 0, {
  message: 'At least one field must be provided',
  path: ['body'],
});

/**
 * Validate create time block request
 */
export async function validateCreateTimeBlock(req: NextRequest): Promise<{ data: z.infer<typeof createTimeBlockSchema> } | NextResponse> {
  try {
    const body = await req.json();
    const result = createTimeBlockSchema.safeParse(body);

    if (!result.success) {
      const errors = result.error.errors.map(err => `${err.path.join('.')}: ${err.message}`).join(', ');
      return errorResponse(errors, ErrorCode.VALIDATION_ERROR, 400);
    }

    // Additional validation: end time must be after start time
    if (result.data.startTime >= result.data.endTime) {
      return errorResponse('End time must be after start time', ErrorCode.VALIDATION_ERROR, 400);
    }

    return { data: result.data };
  } catch (error) {
    return errorResponse('Invalid request body', ErrorCode.BAD_REQUEST, 400);
  }
}

/**
 * Validate update time block request
 */
export async function validateUpdateTimeBlock(req: NextRequest): Promise<{ data: z.infer<typeof updateTimeBlockSchema> } | NextResponse> {
  try {
    const body = await req.json();
    const result = updateTimeBlockSchema.safeParse(body);

    if (!result.success) {
      const errors = result.error.errors.map(err => `${err.path.join('.')}: ${err.message}`).join(', ');
      return errorResponse(errors, ErrorCode.VALIDATION_ERROR, 400);
    }

    // Additional validation: if both start and end time are provided, end time must be after start time
    if (result.data.startTime && result.data.endTime && result.data.startTime >= result.data.endTime) {
      return errorResponse('End time must be after start time', ErrorCode.VALIDATION_ERROR, 400);
    }

    return { data: result.data };
  } catch (error) {
    return errorResponse('Invalid request body', ErrorCode.BAD_REQUEST, 400);
  }
}
