"use client"

import { format, isSameDay, isToday, parseISO } from 'date-fns';
import { Card, CardContent } from '@/components/ui/card';
import { TimeBlock } from '@/lib/types';
import { cn } from '@/lib/utils';
import { usePreferences } from '@/hooks/use-preferences';

interface WeekViewTabProps {
  loading: boolean;
  weekDates: Date[];
  selectedDate: Date;
  timeBlocks: TimeBlock[] | undefined;
  setSelectedDate: (date: Date) => void;
  setBlockToEdit: (block: TimeBlock) => void;
  setIsFormOpen: (isOpen: boolean) => void;
  getCategoryColor: (category: string | object, opacity?: number) => string;
  getTextColor: (bgColor: string) => string;
}

export function WeekViewTab({
  loading,
  weekDates,
  selectedDate,
  timeBlocks,
  setSelectedDate,
  setBlockToEdit,
  setIsFormOpen,
  getCategoryColor,
  getTextColor
}: WeekViewTabProps) {
  // Get user preferences for time formatting
  const { formatTime } = usePreferences();
  // Generate week view grid
  const renderWeekView = () => {
    if (weekDates.length === 0) {
      return (
        <div className="flex items-center justify-center h-[300px]">
          <div className="text-muted-foreground">Loading week view...</div>
        </div>
      );
    }

    // Show all 24 hours for better visibility
    const hours = Array.from({ length: 24 }, (_, i) => i);

    return (
      <div className="relative">
        {/* Fixed header with days */}
        {/* Increased min width for day columns */}
        <div className="grid grid-cols-[60px_repeat(7,minmax(150px,1fr))] border-b sticky top-0 bg-background z-20 ">
          <div className="sticky left-0 bg-background z-30 border-r"></div>
          {weekDates.map((date, i) => (
            <div
              key={i}
              className={cn(
                "p-2 text-center border-r",
                isSameDay(date, selectedDate) && "bg-primary/5"
              )}
            >
              <div className="text-xs font-medium">{format(date, 'EEE')}</div>
              <div
                className={cn(
                  "text-sm sm:text-base font-medium",
                  isToday(date) && "text-primary"
                )}
              >
                {format(date, 'd')}
              </div>
            </div>
          ))}
        </div>

        {/* Time grid */}
        <div className="relative ">
          {hours.map((hour) => (
            // Increased min width for day columns
            <div key={hour} className="grid grid-cols-[60px_repeat(7,minmax(150px,1fr))] border-b">
              {/* Hour label - fixed on scroll */}
              <div className="sticky left-0 bg-background z-10 p-2 text-xs text-muted-foreground flex items-center justify-end pr-2 border-r">
                {formatTime(`${hour.toString().padStart(2, '0')}:00`)}
              </div>

              {/* Day columns */}
              {weekDates.map((date, dayIndex) => {
                const blocksForHour = timeBlocks?.filter(block => {
                  try {
                    const blockDate = parseISO(block.date);
                    if (!isSameDay(blockDate, date)) return false;

                    const [startHour] = block.startTime.split(':').map(Number);
                    const [endHour] = block.endTime.split(':').map(Number);

                    // Include blocks that overlap with this hour
                    return (startHour <= hour && endHour > hour) ||
                           (startHour === hour);
                  } catch (error) {
                    console.error("Error processing block:", error);
                    return false;
                  }
                }) || [];

                return (
                  <div
                    key={dayIndex}
                    className={cn(
                      "p-1 border-r min-h-[60px] relative",
                      isSameDay(date, selectedDate) && "bg-primary/5"
                    )}
                  >
                    {blocksForHour.map((block, i) => (
                      <div
                        key={i}
                        className="text-xs p-1.5 rounded mb-1 shadow-sm cursor-pointer overflow-hidden hover:shadow-md transition-shadow relative"
                        style={{
                          backgroundColor: getCategoryColor(block.category, 0.85),
                          color: getTextColor(getCategoryColor(block.category)),
                          borderLeft: `3px solid ${getCategoryColor(block.category)}`
                        }}
                        onClick={() => {
                          // Set the selected date to this block's date
                          const blockDate = parseISO(block.date);
                          setSelectedDate(blockDate);
                          setBlockToEdit(block);
                          setIsFormOpen(true);
                        }}
                      >
                        <div className="font-medium text-[11px] sm:text-xs">{formatTime(block.startTime)} - {formatTime(block.endTime)}</div>
                        <div className="line-clamp-2 text-[10px] sm:text-xs mt-0.5">{block.note}</div>
                      </div>
                    ))}
                  </div>
                );
              })}
            </div>
          ))}
        </div>
      </div>
    );
  };

  return (
    <Card className="shadow-sm overflow-hidden border-0">
      <CardContent className="p-0">
        {loading ? (
          <div className="flex flex-col items-center justify-center h-40 gap-4 p-6">
            <p className="text-sm text-muted-foreground">Loading time blocks...</p>
          </div>
        ) : (
          <div className="overflow-auto">
            <div className="min-w-[1110px]">
              {renderWeekView()}
            </div>
            <div className="h-4"></div> {/* Add some bottom padding */}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
