"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { PlusIcon, Calendar, RefreshCw } from "lucide-react";
import { useRoutines } from "@/hooks/use-routines";
import { RoutineForm } from "@/components/routines/routine-form";
import { RoutineWeekView } from "@/components/routines/routine-week-view";
import { RoutineDetails } from "@/components/routines/routine-details";
import {
  AlertDialog,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";

export function Routines() {
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [selectedRoutine, setSelectedRoutine] = useState<any>(null);
  const [detailsRoutine, setDetailsRoutine] = useState<any>(null);
  const [deleteRoutineId, setDeleteRoutineId] = useState<string | null>(null);
  const [debugInfo, setDebugInfo] = useState<string>("");
  const { routines, isLoading, error, createRoutine, updateRoutine, deleteRoutine, refetch } = useRoutines();

  // Set debug info when routines change, but don't trigger additional fetches
  useEffect(() => {
    // Only update debug info, don't trigger any API calls
    if (Array.isArray(routines)) {
      setDebugInfo(`Found ${routines.length} routines`);
    } else {
      setDebugInfo("No routines data available");
    }
  }, [routines]);

  // Simplified create routine handler to avoid excessive API calls
  const handleCreateRoutine = async (data: any) => {
    try {
      // Directly call the API without optimistic updates to avoid state conflicts
      await createRoutine(data);
      setIsFormOpen(false);

      // No need to call refetch() as the createRoutine function already does this
    } catch (err) {
      console.error("Error creating routine:", err);
      // No need to refetch here as the error is already handled in the hook
    }
  };

  const handleUpdateRoutine = async (data: any) => {
    if (selectedRoutine) {
      try {
        // Directly call the API without optimistic updates to avoid state conflicts
        await updateRoutine(selectedRoutine.id, data);

        setSelectedRoutine(null);
        setIsFormOpen(false);

        // No need to call refetch() as the updateRoutine function already does this
      } catch (err) {
        console.error("Error updating routine:", err);
        // No need to refetch here as the error is already handled in the hook
      }
    }
  };

  const handleEditRoutine = (routine: any) => {
    setSelectedRoutine(routine);
    setIsFormOpen(true);
  };

  const handleShowDetails = (routine: any) => {
    setDetailsRoutine(routine);
  };

  const handleDeleteRoutine = async () => {
    if (deleteRoutineId) {
      try {
        // Directly call the API without optimistic updates to avoid state conflicts
        await deleteRoutine(deleteRoutineId);

        // No need to call refetch() as the deleteRoutine function already does this
      } catch (err) {
        console.error("Error deleting routine:", err);
        // No need to refetch here as the error is already handled in the hook
      }
      setDeleteRoutineId(null);
    }
  };

  return (
    <div className="max-w-7xl mx-auto px-2 py-2 relative">
      <div className="rounded-xl bg-white dark:bg-zinc-900 shadow-lg border overflow-hidden">
        {isLoading ? (
          <div className="flex items-center justify-center h-[400px]">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
          </div>
        ) : error ? (
          <div className="flex flex-col items-center justify-center h-[400px] text-center px-8">
            <div className="mb-6 p-4 rounded-xl bg-red-50 dark:bg-red-950/50">
              <Calendar className="h-12 w-12 text-red-600 dark:text-red-400" />
            </div>
            <h3 className="text-2xl font-bold tracking-tight mb-3">Unable to Load Routines</h3>
            <p className="text-muted-foreground text-lg max-w-md mb-8">
              There was a problem loading your routines. Please try refreshing the page.
            </p>
            <Button
              onClick={() => refetch()}
              className="h-11 px-6 text-base bg-blue-600 hover:bg-blue-700 text-white font-medium"
            >
              <RefreshCw className="w-5 h-5 mr-2" />
              Try Again
            </Button>
          </div>
        ) : routines.length > 0 ? (
          <>
            <RoutineWeekView
              routines={routines}
              onEditRoutine={handleShowDetails}
              onDeleteRoutine={(id) => setDeleteRoutineId(id)}
            />
            {/* Debug info - can be removed in production */}
            <div className="mt-4 text-xs text-right text-muted-foreground px-4 pb-2">
              {debugInfo}
            </div>
          </>
        ) : (
          <div className="flex flex-col items-center justify-center h-[600px] text-center px-8">
            <div className="mb-6 p-4 rounded-xl bg-muted/10 dark:bg-muted/5">
              <Calendar className="h-12 w-12 text-muted-foreground" />
            </div>
            <h3 className="text-2xl font-bold tracking-tight mb-3">No Routines Added Yet</h3>
            <p className="text-muted-foreground text-lg max-w-md mb-8">
              Create your first routine to organize your weekly schedule.
              Your routines will appear here in a weekly calendar view.
            </p>
            <Button
              onClick={() => {
                setSelectedRoutine(null);
                setIsFormOpen(true);
              }}
              className="h-11 px-6 text-base bg-blue-600 hover:bg-blue-700 text-white font-medium"
            >
              <PlusIcon className="w-5 h-5 mr-2" />
              Create First Routine
            </Button>
            {debugInfo && (
              <div className="mt-6 text-sm text-muted-foreground">{debugInfo}</div>
            )}
          </div>
        )}
      </div>

      {/* Sticky Add Routine Button */}
      <Button
        size="sm"
        className="fixed bottom-6 right-6 h-10 w-10 rounded-full shadow-lg bg-blue-600 hover:bg-blue-700 text-white z-50"
        onClick={() => {
          setSelectedRoutine(null);
          setIsFormOpen(true);
        }}
        aria-label="Add Routine"
      >
        <PlusIcon className="w-5 h-5" />
      </Button>

      {/* Routine Details Modal */}
      {detailsRoutine && (
        <RoutineDetails
          routine={detailsRoutine}
          isOpen={!!detailsRoutine}
          onClose={() => setDetailsRoutine(null)}
          onEdit={(routine) => {
            setSelectedRoutine(routine);
            setDetailsRoutine(null);
            setIsFormOpen(true);
          }}
          onDelete={(id) => {
            setDeleteRoutineId(id);
            setDetailsRoutine(null);
          }}
        />
      )}

      {/* Add/Edit Routine Form */}
      <RoutineForm
        isOpen={isFormOpen}
        onClose={() => {
          setIsFormOpen(false);
          setSelectedRoutine(null);
        }}
        onSubmit={selectedRoutine ? handleUpdateRoutine : handleCreateRoutine}
        routine={selectedRoutine}
      />

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={!!deleteRoutineId} onOpenChange={() => setDeleteRoutineId(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This will permanently delete the routine. This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <Button variant="destructive" onClick={handleDeleteRoutine}>
              Delete
            </Button>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
}
