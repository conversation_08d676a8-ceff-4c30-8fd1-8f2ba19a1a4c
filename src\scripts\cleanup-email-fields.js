/**
 * Clean up any email fields from existing users in the database
 * This script removes email fields from all users to prevent conflicts
 * Run with: node src/scripts/cleanup-email-fields.js
 */

const mongoose = require('mongoose');
const path = require('path');
const fs = require('fs');

// Load environment variables from .env file
const envPath = path.join(__dirname, '../../.env');
if (fs.existsSync(envPath)) {
  const envContent = fs.readFileSync(envPath, 'utf8');
  const envLines = envContent.split('\n');
  envLines.forEach(line => {
    const equalIndex = line.indexOf('=');
    if (equalIndex > 0) {
      const key = line.substring(0, equalIndex).trim();
      const value = line.substring(equalIndex + 1).trim();
      if (key && value) {
        process.env[key] = value;
      }
    }
  });
}

const MONGODB_URI = process.env.MONGODB_URI;

async function cleanupEmailFields() {
  try {
    console.log('Connecting to MongoDB...');
    await mongoose.connect(MONGODB_URI);
    console.log('Connected to MongoDB successfully');
    
    const db = mongoose.connection.db;
    const collection = db.collection('users');
    
    // Check for users with email fields
    console.log('\n🔍 Checking for users with email fields...');
    const usersWithEmail = await collection.find({ email: { $exists: true } }).toArray();
    
    if (usersWithEmail.length > 0) {
      console.log(`Found ${usersWithEmail.length} users with email fields:`);
      usersWithEmail.forEach((user, i) => {
        console.log(`${i + 1}. ID: ${user._id}, Email: ${user.email}, AccessKey: ${user.accessKey || 'none'}`);
      });
      
      // Remove email fields from all users
      console.log('\n🧹 Removing email fields from all users...');
      const result = await collection.updateMany(
        { email: { $exists: true } },
        { $unset: { email: "" } }
      );
      
      console.log(`✅ Removed email field from ${result.modifiedCount} users`);
    } else {
      console.log('✅ No users with email fields found');
    }
    
    // Check for any remaining email indexes
    console.log('\n🔍 Checking for email indexes...');
    const indexes = await collection.indexes();
    const emailIndexes = indexes.filter(index => index.key && index.key.email !== undefined);
    
    if (emailIndexes.length > 0) {
      console.log('Found email indexes:');
      emailIndexes.forEach(index => {
        console.log(`- ${index.name}: ${JSON.stringify(index.key)}`);
      });
      
      // Drop email indexes
      for (const index of emailIndexes) {
        try {
          await collection.dropIndex(index.name);
          console.log(`✅ Dropped index: ${index.name}`);
        } catch (error) {
          console.log(`❌ Failed to drop index ${index.name}:`, error.message);
        }
      }
    } else {
      console.log('✅ No email indexes found');
    }
    
    // Test user creation
    console.log('\n🧪 Testing user creation...');
    try {
      const testResult = await collection.insertOne({
        accessKey: 'test-cleanup-' + Date.now(),
        name: 'Test User',
        preferences: {
          timeInterval: '60',
          startHour: '0',
          endHour: '23',
          timeFormat: '12',
          darkMode: false,
          syncEnabled: true,
          emailNotifications: true,
          useCustomTimeBlocks: false,
          customTimeBlocks: []
        },
        createdAt: new Date(),
        updatedAt: new Date()
      });
      
      console.log('✅ Test user creation successful!');
      
      // Clean up test user
      await collection.deleteOne({ _id: testResult.insertedId });
      console.log('🧹 Test user cleaned up');
      
    } catch (testError) {
      console.log('❌ Test user creation failed:', testError.message);
      if (testError.message.includes('E11000')) {
        console.log('⚠️  Duplicate key error still exists. This might be a different issue.');
        console.log('Error details:', testError);
      }
    }
    
    console.log('\n✅ Email field cleanup completed!');
    
  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    // Close the connection
    if (mongoose.connection.readyState !== 0) {
      await mongoose.disconnect();
      console.log('\nDisconnected from MongoDB');
    }
  }
}

// Run the function
cleanupEmailFields();
