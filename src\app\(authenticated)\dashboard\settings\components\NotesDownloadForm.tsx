"use client"

import { useState, useEffect, useCallback } from 'react';
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useTimeBlocks } from '@/hooks/use-time-blocks';
import { format, startOfMonth, endOfMonth, parseISO, isValid, subMonths, differenceInMinutes } from 'date-fns';
import { toast } from 'sonner';
import { Loader2 } from 'lucide-react';

export function NotesDownloadForm() {
  const { timeBlocks, loading: blocksLoading, refreshTimeBlocks } = useTimeBlocks();
  const [selectedMonth, setSelectedMonth] = useState<string>('');
  const [isDownloading, setIsDownloading] = useState(false);
  const [availableMonths, setAvailableMonths] = useState<Array<{ value: string; label: string }>>([]);
  const [hasInitialized, setHasInitialized] = useState(false);

  // Generate all months for the current year and previous year
  const generateMonthOptions = useCallback(() => {
    const months: Array<{ value: string; label: string }> = [];
    const currentDate = new Date();
    
    // Add current year's months
    for (let i = 0; i < 12; i++) {
      const date = subMonths(currentDate, i);
      months.push({
        value: format(date, 'yyyy-MM'),
        label: format(date, 'MMMM yyyy')
      });
    }

    // Add previous year's months if we have data from that year
    if (timeBlocks && timeBlocks.length > 0) {
      const oldestDate = timeBlocks.reduce((oldest, block) => {
        const blockDate = new Date(block.startTime);
        return blockDate < oldest ? blockDate : oldest;
      }, new Date());

      if (oldestDate.getFullYear() < currentDate.getFullYear()) {
        const prevYear = currentDate.getFullYear() - 1;
        for (let i = 0; i < 12; i++) {
          const date = new Date(prevYear, i, 1);
          months.push({
            value: format(date, 'yyyy-MM'),
            label: format(date, 'MMMM yyyy')
          });
        }
      }
    }

    // Sort months in descending order (most recent first)
    return months.sort((a, b) => b.value.localeCompare(a.value));
  }, [timeBlocks]);

  // Initialize data and set up month options
  useEffect(() => {
    if (!hasInitialized && !blocksLoading) {
      const initializeData = async () => {
        try {
          if (!timeBlocks || timeBlocks.length === 0) {
            await refreshTimeBlocks(true);
          }
          setAvailableMonths(generateMonthOptions());
          setHasInitialized(true);
        } catch (error) {
          console.error('Error initializing data:', error);
          toast.error('Failed to load notes data');
        }
      };
      initializeData();
    }
  }, [hasInitialized, blocksLoading, timeBlocks, refreshTimeBlocks, generateMonthOptions]);

  // Update month options when time blocks change
  useEffect(() => {
    if (timeBlocks) {
      setAvailableMonths(generateMonthOptions());
    }
  }, [timeBlocks, generateMonthOptions]);

  const downloadNotes = useCallback(async (type: 'monthly' | 'lifetime') => {
    if (!timeBlocks || timeBlocks.length === 0) {
      toast.error('No notes available to download');
      return;
    }
    
    setIsDownloading(true);
    try {
      let filteredBlocks = [...timeBlocks];
      let fileName = '';
      let periodDescription = '';

      if (type === 'monthly') {
        if (!selectedMonth) {
          toast.error('Please select a month');
          return;
        }
        const [year, month] = selectedMonth.split('-');
        const startDate = startOfMonth(new Date(parseInt(year), parseInt(month) - 1));
        const endDate = endOfMonth(startDate);
        
        filteredBlocks = timeBlocks.filter(block => {
          const blockDate = parseISO(block.date);
          return blockDate >= startDate && blockDate <= endDate;
        });

        if (filteredBlocks.length === 0) {
          toast.error('No notes found for the selected month');
          return;
        }

        fileName = `notes-${format(startDate, 'yyyy-MM')}.txt`;
        periodDescription = format(startDate, 'MMMM yyyy');
      } else {
        // For lifetime, get the date range from the data
        const dates = timeBlocks.map(block => parseISO(block.date));
        const startDate = new Date(Math.min(...dates.map(d => d.getTime())));
        const endDate = new Date(Math.max(...dates.map(d => d.getTime())));
        
        fileName = `notes-${format(startDate, 'yyyy-MM')}-to-${format(endDate, 'yyyy-MM')}.txt`;
        periodDescription = `${format(startDate, 'MMMM yyyy')} to ${format(endDate, 'MMMM yyyy')}`;
      }

      // Sort blocks by date and time
      filteredBlocks.sort((a, b) => {
        const dateCompare = a.date.localeCompare(b.date);
        if (dateCompare !== 0) return dateCompare;
        return a.startTime.localeCompare(b.startTime);
      });

      // Generate text content with header
      let content = `Notes Export - ${periodDescription}\n`;
      content += `Generated on: ${format(new Date(), 'MMMM d, yyyy h:mm a')}\n`;
      content += `${'='.repeat(60)}\n\n`;

      let currentDate = '';
      let totalDuration = 0;
      let categoryStats = new Map<string, number>();

      filteredBlocks.forEach(block => {
        const blockDate = parseISO(block.date);
        const formattedDate = format(blockDate, 'MMMM d, yyyy');
        
        // Add date header if it's a new date
        if (formattedDate !== currentDate) {
          content += `\n${'-'.repeat(60)}\n`;
          content += `${formattedDate}\n`;
          content += `${'-'.repeat(60)}\n\n`;
          currentDate = formattedDate;
        }

        // Calculate duration
        const [startHour, startMinute] = block.startTime.split(':').map(Number);
        const [endHour, endMinute] = block.endTime.split(':').map(Number);
        const startTotalMinutes = startHour * 60 + startMinute;
        let endTotalMinutes = endHour * 60 + endMinute;
        if (endTotalMinutes <= startTotalMinutes) {
          endTotalMinutes += 24 * 60;
        }
        const duration = endTotalMinutes - startTotalMinutes;
        totalDuration += duration;

        // Update category stats
        const category = block.categoryData?.name || block.category || 'Uncategorized';
        categoryStats.set(category, (categoryStats.get(category) || 0) + duration);

        // Format the block content
        content += `Time: ${block.startTime} - ${block.endTime} (${duration} minutes)\n`;
        content += `Category: ${category}\n`;
        
        if (block.note) {
          content += `Note: ${block.note.trim()}\n`;
        }
        
        if (block.isTodo !== undefined) {
          content += `Type: ${block.isTodo ? 'Todo' : 'Note'}\n`;
          if (block.isTodo && block.isCompleted !== undefined) {
            content += `Status: ${block.isCompleted ? 'Completed' : 'Pending'}\n`;
          }
        }
        
        content += '\n';
      });

      // Add summary at the end
      content += `\n${'='.repeat(60)}\n`;
      content += `Summary:\n`;
      content += `Total Notes: ${filteredBlocks.length}\n`;
      content += `Total Time: ${Math.floor(totalDuration / 60)}h ${totalDuration % 60}m\n`;
      
      // Add category breakdown
      if (categoryStats.size > 0) {
        content += '\nCategory Breakdown:\n';
        categoryStats.forEach((duration, category) => {
          const hours = Math.floor(duration / 60);
          const minutes = duration % 60;
          content += `${category}: ${hours}h ${minutes}m\n`;
        });
      }
      
      content += `\nPeriod: ${periodDescription}\n`;
      content += `Generated: ${format(new Date(), 'MMMM d, yyyy h:mm a')}\n`;

      // Create and download file
      const blob = new Blob([content], { type: 'text/plain;charset=utf-8' });
      const url = window.URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = fileName;
      document.body.appendChild(a);
      a.click();
      window.URL.revokeObjectURL(url);
      document.body.removeChild(a);

      toast.success(`Successfully downloaded ${filteredBlocks.length} notes`);
    } catch (error) {
      console.error('Error downloading notes:', error);
      toast.error('Failed to download notes. Please try again.');
    } finally {
      setIsDownloading(false);
    }
  }, [timeBlocks, selectedMonth]);

  if (blocksLoading && !hasInitialized) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center p-6">
          <Loader2 className="h-6 w-6 animate-spin text-muted-foreground" />
          <span className="ml-2 text-muted-foreground">Loading notes...</span>
        </CardContent>
      </Card>
    );
  }

  if (!timeBlocks || timeBlocks.length === 0) {
    return (
      <Card>
        <CardContent className="p-6">
          <p className="text-center text-muted-foreground">No notes available to download.</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="border-0 shadow-sm bg-gradient-to-r from-purple-50/50 to-pink-50/50 dark:from-purple-950/20 dark:to-pink-950/20">
      <CardHeader className="pb-4">
        <CardTitle className="text-xl font-semibold text-purple-800 dark:text-purple-200">
          Download Notes
        </CardTitle>
        <CardDescription className="text-purple-600 dark:text-purple-400">
          Export your notes as a text file. Choose monthly or all-time download.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-3">
          <label className="text-sm font-medium">Select Month (for monthly download)</label>
          <Select
            value={selectedMonth}
            onValueChange={setSelectedMonth}
            disabled={isDownloading || availableMonths.length === 0}
          >
            <SelectTrigger className="w-full">
              <SelectValue placeholder="Select a month" />
            </SelectTrigger>
            <SelectContent className="max-h-60">
              {availableMonths.map(month => (
                <SelectItem key={month.value} value={month.value}>
                  {month.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          {availableMonths.length === 0 && (
            <p className="text-sm text-muted-foreground">No months with notes available</p>
          )}
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 pt-2">
          <Button
            onClick={() => downloadNotes('monthly')}
            disabled={isDownloading || !selectedMonth || availableMonths.length === 0}
            className="bg-purple-600 hover:bg-purple-700"
          >
            {isDownloading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Downloading...
              </>
            ) : (
              'Download Monthly'
            )}
          </Button>
          <Button
            onClick={() => downloadNotes('lifetime')}
            disabled={isDownloading || availableMonths.length === 0}
            variant="outline"
            className="border-purple-300 hover:bg-purple-100 dark:border-purple-700 dark:hover:bg-purple-900"
          >
            {isDownloading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Downloading...
              </>
            ) : (
              'Download All'
            )}
          </Button>
        </div>
      </CardContent>
    </Card>
  );
} 