import { NextRequest } from 'next/server';
import { auth<PERSON>ontroller } from '@/core/controllers';
import { generateAccessKey } from '@/utils/auth';
import { userRepository } from '@/core/repositories';
import { successResponse, errorResponse } from '@/utils/response';
import { ErrorCode } from '@/constants/error-codes';
import logger from '@/utils/logger';
import { z } from 'zod';

// Tell Next.js this route should always be dynamically rendered
export const dynamic = 'force-dynamic';

// Enhanced registration schema
const enhancedRegisterSchema = z.object({
  mode: z.enum(['generate', 'save']).default('generate'),
  accessKey: z.string().optional(),
  name: z.string().optional().default(''),
});

/**
 * POST /api/auth/register - Generate or save access key
 */
export async function POST(req: NextRequest) {
  try {
    const body = await req.json().catch(() => ({}));
    const { mode, accessKey, name } = enhancedRegisterSchema.parse(body);

    if (mode === 'generate') {
      // Generate a new access key without saving
      logger.info('Generating new access key without saving...');

      let newAccessKey: string;
      let attempts = 0;
      const maxAttempts = 10;

      do {
        newAccessKey = generateAccessKey();
        attempts++;

        if (attempts > maxAttempts) {
          return errorResponse(
            'Failed to generate unique access key',
            ErrorCode.INTERNAL_SERVER_ERROR,
            500
          );
        }
      } while (await userRepository.accessKeyExists(newAccessKey));

      logger.info(`Generated unique access key: ${newAccessKey}`);

      return successResponse({
        accessKey: newAccessKey,
        message: 'Access key generated successfully'
      });
    } else if (mode === 'save') {
      // Save the access key and create user
      if (!accessKey) {
        logger.error('Save mode called without access key');
        return errorResponse(
          'Access key is required for save mode',
          ErrorCode.VALIDATION_ERROR,
          400
        );
      }

      logger.info(`Attempting to save access key: ${accessKey}`);

      // Check if access key already exists
      const keyExists = await userRepository.accessKeyExists(accessKey);
      logger.info(`Access key exists check result: ${keyExists}`);

      if (keyExists) {
        logger.warn(`Access key already exists: ${accessKey}`);
        return errorResponse(
          'Access key already exists',
          ErrorCode.DUPLICATE_ACCESS_KEY,
          409
        );
      }

      // Create user with the provided access key
      logger.info(`Creating user with access key: ${accessKey} and name: ${name || 'none'}`);
      const result = await authController.registerWithKey({ accessKey, name });
      logger.info('User creation completed successfully');
      return result;
    } else {
      return errorResponse(
        'Invalid mode',
        ErrorCode.VALIDATION_ERROR,
        400
      );
    }
  } catch (error) {
    if (error instanceof z.ZodError) {
      const firstError = error.errors[0];
      return errorResponse(
        firstError.message,
        ErrorCode.VALIDATION_ERROR,
        400
      );
    }

    logger.error('Registration error:', error);
    return errorResponse(
      'An unexpected error occurred',
      ErrorCode.INTERNAL_SERVER_ERROR,
      500
    );
  }
}
