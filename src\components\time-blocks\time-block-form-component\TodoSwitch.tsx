"use client"

import { FormControl, FormField, FormItem, FormLabel } from '@/components/ui/form';
import { Switch } from '@/components/ui/switch';
import { UseFormReturn } from 'react-hook-form';

interface TodoSwitchProps {
  form: UseFormReturn<any>;
}

export function TodoSwitch({ form }: TodoSwitchProps) {
  return (
    <FormField
      control={form.control}
      name="isTodo"
      render={({ field }) => (
        <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3 space-y-0">
          <div className="space-y-0.5">
            <FormLabel className="text-sm font-medium">Mark as Todo</FormLabel>
            <div className="text-xs text-muted-foreground">
              Mark this time block as a todo task
            </div>
          </div>
          <FormControl>
            <Switch
              checked={field.value}
              onCheckedChange={field.onChange}
              className="data-[state=checked]:bg-green-500"
            />
          </FormControl>
        </FormItem>
      )}
    />
  );
}
