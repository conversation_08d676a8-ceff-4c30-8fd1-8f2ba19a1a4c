/**
 * Direct login test script
 * Run with: node src/scripts/test-login.js <email> <password>
 */

const mongoose = require('mongoose');
const bcrypt = require('bcrypt');

// MongoDB connection string - update this with your actual connection string
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/time-tracker-app';

async function testLogin(email, password) {
  try {
    console.log('Connecting to MongoDB...');
    await mongoose.connect(MONGODB_URI);
    console.log('Connected to MongoDB successfully');
    
    // Check if the users collection exists
    const collections = await mongoose.connection.db.listCollections({ name: 'users' }).toArray();
    if (collections.length === 0) {
      console.error('❌ Users collection does not exist!');
      return;
    }
    
    console.log(`Testing login for email: ${email}`);
    
    // Find user directly from the collection
    const user = await mongoose.connection.db.collection('users').findOne({ email });
    
    if (!user) {
      console.error(`❌ User with email ${email} not found!`);
      return;
    }
    
    console.log('✅ User found:');
    console.log(`- ID: ${user._id}`);
    console.log(`- Name: ${user.name}`);
    console.log(`- Email: ${user.email}`);
    
    // Check if password field exists and is not empty
    if (!user.password) {
      console.error('❌ User has no password set!');
      return;
    }
    
    console.log(`- Password hash length: ${user.password.length}`);
    
    // Test password comparison
    console.log('\nTesting password comparison...');
    
    try {
      const isMatch = await bcrypt.compare(password, user.password);
      
      if (isMatch) {
        console.log('✅ Password matches!');
      } else {
        console.log('❌ Password does not match!');
        
        // Try with trimmed password
        const isMatchTrimmed = await bcrypt.compare(password.trim(), user.password);
        if (isMatchTrimmed) {
          console.log('✅ Password matches after trimming!');
        }
      }
    } catch (bcryptError) {
      console.error('❌ Error comparing passwords:', bcryptError);
    }
    
  } catch (error) {
    console.error('Error:', error);
  } finally {
    // Close the connection
    if (mongoose.connection.readyState !== 0) {
      await mongoose.disconnect();
      console.log('\nDisconnected from MongoDB');
    }
  }
}

// Get email and password from command line arguments
const email = process.argv[2];
const password = process.argv[3];

if (!email || !password) {
  console.error('Please provide email and password as arguments:');
  console.error('node src/scripts/test-login.js <email> <password>');
  process.exit(1);
}

// Run the test
testLogin(email, password);
