"use client"

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { RefreshCw } from 'lucide-react';
import { TimeBlock } from '@/lib/types';
import { CategoryStats } from './CategoryStats';

interface AnalyticsTabProps {
  analyticsPeriod: 'daily' | 'weekly' | 'monthly';
  setAnalyticsPeriod: (period: 'daily' | 'weekly' | 'monthly') => void;
  periodDescription: string;
  filteredBlocks: TimeBlock[];
  weeklyBlocks: TimeBlock[];
  monthlyBlocks: TimeBlock[];
  totalTimeTracked: number;
  categoryStats: any[];
  getTextColor: (bgColor: string) => string;
  onRefresh: () => Promise<void>;
  isRefreshing?: boolean;
}

export function AnalyticsTab({
  analyticsPeriod,
  setAnalyticsPeriod,
  periodDescription,
  filteredBlocks,
  weeklyBlocks,
  monthlyBlocks,
  totalTimeTracked,
  categoryStats,
  getTextColor,
  onRefresh,
  isRefreshing = false
}: AnalyticsTabProps) {
  // Get the blocks for the current period
  const blocksForPeriod =
    analyticsPeriod === 'daily' ? filteredBlocks :
    analyticsPeriod === 'weekly' ? weeklyBlocks :
    monthlyBlocks;

  return (
    <>
      {/* Analytics Period Selector with Refresh Button */}
      <div className="mb-3 flex flex-col sm:flex-row sm:items-center gap-2">
        <Tabs
          value={analyticsPeriod}
          onValueChange={(value) => setAnalyticsPeriod(value as 'daily' | 'weekly' | 'monthly')}
          className="w-full"
        >
          <TabsList className="grid w-full grid-cols-3 max-w-md mx-auto h-8">
            <TabsTrigger value="daily" className="text-xs py-1">Today</TabsTrigger>
            <TabsTrigger value="weekly" className="text-xs py-1">Week</TabsTrigger>
            <TabsTrigger value="monthly" className="text-xs py-1">Month</TabsTrigger>
          </TabsList>
        </Tabs>

        <Button
          variant="outline"
          size="sm"
          className="ml-auto h-8 text-xs"
          onClick={() => onRefresh()}
          disabled={isRefreshing}
        >
          <RefreshCw className={`h-3.5 w-3.5 mr-1 ${isRefreshing ? 'animate-spin' : ''}`} />
          Refresh
        </Button>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
        {/* Total Time Blocks Card */}
        <Card className="shadow-sm border border-border/60 hover:border-border/80 transition-colors">
          <CardHeader className="pb-1 pt-3 px-3">
            <CardTitle className="text-sm font-medium">Total Time Blocks</CardTitle>
            <CardDescription className="text-xs">For {periodDescription}</CardDescription>
          </CardHeader>
          <CardContent className="pb-3 px-3">
            <div className="text-xl font-bold">
              {blocksForPeriod.length}
            </div>
          </CardContent>
        </Card>

        {/* Total Hours Tracked Card */}
        <Card className="shadow-sm border border-border/60 hover:border-border/80 transition-colors">
          <CardHeader className="pb-1 pt-3 px-3">
            <CardTitle className="text-sm font-medium">Total Hours Tracked</CardTitle>
            <CardDescription className="text-xs">For {periodDescription}</CardDescription>
          </CardHeader>
          <CardContent className="pb-3 px-3">
            <div className="text-xl font-bold flex items-baseline gap-1">
              {Math.floor(totalTimeTracked / 60)}h
              {totalTimeTracked % 60 > 0 && (
                <span className="text-lg">{totalTimeTracked % 60}m</span>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Most Used Category Card */}
        <Card className="shadow-sm border border-border/60 hover:border-border/80 transition-colors">
          <CardHeader className="pb-1 pt-3 px-3">
            <CardTitle className="text-sm font-medium">Most Used Category</CardTitle>
            <CardDescription className="text-xs">For {periodDescription}</CardDescription>
          </CardHeader>
          <CardContent className="pb-3 px-3">
            {categoryStats.length > 0 ? (
              <div className="flex items-center gap-2">
                <div
                  className="w-3 h-3 rounded-full" style={{ backgroundColor: categoryStats[0].color }} // Dynamic color, must use inline style as Tailwind cannot generate dynamic classes
                ></div>
                <div className="text-lg font-bold">{categoryStats[0].category}</div>
              </div>
            ) : (
              <div className="text-lg font-bold text-muted-foreground">None</div>
            )}
          </CardContent>
        </Card>

        {/* Todo Statistics */}
        <Card className="shadow-sm border border-border/60 hover:border-border/80 transition-colors">
          <CardHeader className="pb-1 pt-3 px-3">
            <CardTitle className="text-sm font-medium">Todo Completion</CardTitle>
            <CardDescription className="text-xs">For {periodDescription}</CardDescription>
          </CardHeader>
          <CardContent className="pb-3 px-3">
            <div className="text-xl font-bold flex flex-col">
              <div className="flex items-baseline gap-1">
                {blocksForPeriod.filter(block => block.isTodo && block.isCompleted).length}/
                {blocksForPeriod.filter(block => block.isTodo).length}
              </div>
              <div className="text-xs text-muted-foreground mt-0.5">
                {blocksForPeriod.filter(block => block.isTodo).length > 0
                  ? `${Math.round((blocksForPeriod.filter(block => block.isTodo && block.isCompleted).length / blocksForPeriod.filter(block => block.isTodo).length) * 100)}% completed`
                  : "No todos"}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Pending Todos */}
        <Card className="shadow-sm border border-border/60 hover:border-border/80 transition-colors">
          <CardHeader className="pb-1 pt-3 px-3">
            <CardTitle className="text-sm font-medium">Pending Todos</CardTitle>
            <CardDescription className="text-xs">For {periodDescription}</CardDescription>
          </CardHeader>
          <CardContent className="pb-3 px-3">
            <div className="text-xl font-bold">
              {blocksForPeriod.filter(block => block.isTodo && !block.isCompleted).length}
            </div>
            <div className="text-xs text-muted-foreground mt-0.5">
              tasks remaining
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Category Stats Component */}
      <div className="mt-6 mb-2">
        <h3 className="text-lg font-semibold mb-3">Time Distribution</h3>
        <div className="bg-card rounded-lg p-4 border border-border/60 shadow-sm">
          <CategoryStats
            categoryStats={categoryStats}
            totalMinutes={totalTimeTracked}
            getTextColor={getTextColor}
          />
        </div>
      </div>
    </>
  );
}
