import { NextRequest, NextResponse } from 'next/server';
import connectDB from "@/config/database";
import { verifyAuth } from "@/utils/auth";
import mongoose from "mongoose";
import { format } from "date-fns";
import logger from "@/utils/logger";

// Tell Next.js this route should always be dynamically rendered
export const dynamic = 'force-dynamic';

/**
 * GET /api/todos - Get all todos for a user
 * This includes todos created from routines
 */
export async function GET(req: NextRequest) {
  try {
    // Verify user is authenticated
    const { authenticated, user } = verifyAuth(req);
    if (!authenticated || !user) {
      return NextResponse.json({ message: "Unauthorized" }, { status: 401 });
    }

    await connectDB();

    // Get all time blocks marked as todos for the user
    // This includes both manually created todos and todos created from routines
    const todos = await mongoose.connection
      .collection("timeblocks")
      .find({
        userId: new mongoose.Types.ObjectId(user.id),
        isTodo: true
      })
      .sort({ date: 1, startTime: 1 }) // Sort by date and then start time
      .toArray();

    // Transform MongoDB _id to id for client consumption
    const formattedTodos = todos.map(todo => ({
      ...todo,
      id: todo._id.toString(),
      _id: undefined
    }));

    return NextResponse.json(formattedTodos);
  } catch (error) {
    logger.error("Error getting todos:", error);
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    );
  }
}

/**
 * POST /api/todos - Create a new todo
 */
export async function POST(req: NextRequest) {
  try {
    // Verify user is authenticated
    const { authenticated, user } = verifyAuth(req);
    if (!authenticated || !user) {
      return NextResponse.json({ message: "Unauthorized" }, { status: 401 });
    }

    const body = await req.json();

    await connectDB();

    // Always ensure isTodo is set to true
    const todoData = {
      ...body,
      isTodo: true,
      userId: new mongoose.Types.ObjectId(user.id),
      createdAt: new Date(),
      updatedAt: new Date()
    };

    // If category is provided as a string ID, convert it to ObjectId
    if (todoData.category && typeof todoData.category === 'string') {
      todoData.category = new mongoose.Types.ObjectId(todoData.category);
    }

    // Create the todo
    const result = await mongoose.connection
      .collection("timeblocks")
      .insertOne(todoData);

    // Return the created todo
    const createdTodo = {
      ...todoData,
      id: result.insertedId.toString(),
      _id: undefined
    };

    return NextResponse.json(createdTodo, { status: 201 });
  } catch (error) {
    logger.error("Error creating todo:", error);
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    );
  }
}
