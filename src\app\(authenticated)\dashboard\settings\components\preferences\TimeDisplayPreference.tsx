"use client"

import { FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { UseFormReturn } from 'react-hook-form';
import { PreferencesFormValues } from '../../schemas';

interface TimeDisplayPreferenceProps {
  form: UseFormReturn<PreferencesFormValues>;
}

export function TimeDisplayPreference({ form }: TimeDisplayPreferenceProps) {
  return (
    <div className="bg-muted/10 p-3 rounded-md">
      <h3 className="text-sm font-medium mb-2">Time Display Settings</h3>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
        <FormField
          control={form.control}
          name="startHour"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="text-sm">Day Start Hour (0-23)</FormLabel>
              <FormControl>
                <Input
                  type="number"
                  min="0"
                  max="23"
                  {...field}
                  className="border focus:border-primary h-8"
                />
              </FormControl>
              <FormDescription className="text-xs">
                First hour to show in day view
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="endHour"
          render={({ field }) => (
            <FormItem>
              <FormLabel className="text-sm">Day End Hour (0-23)</FormLabel>
              <FormControl>
                <Input
                  type="number"
                  min="0"
                  max="23"
                  {...field}
                  className="border focus:border-primary h-8"
                />
              </FormControl>
              <FormDescription className="text-xs">
                Last hour to show in day view
              </FormDescription>
              <FormMessage />
            </FormItem>
          )}
        />
      </div>
    </div>
  );
}
