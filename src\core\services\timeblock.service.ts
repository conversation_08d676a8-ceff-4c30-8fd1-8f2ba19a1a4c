import mongoose from 'mongoose';
import { timeBlockRepository, categoryRepository, routineRepository } from '@/core/repositories';
import { CreateTimeBlockRequest, UpdateTimeBlockRequest } from '@/types/timeblock';
import { ApiRoutine } from '@/types/routine';
import { ApiError } from '@/core/errors/api-error';
import { ErrorCode } from '@/constants/error-codes';
import logger from '@/utils/logger';
import { format } from 'date-fns';

/**
 * TimeBlock service
 */
export class TimeBlockService {
  /**
   * Get all time blocks for a user
   * @param userId User ID
   * @returns Array of time blocks
   */
  async getAllTimeBlocks(userId: string) {
    try {
      await this.ensureRoutineTodosForToday(userId);
      const timeBlocks = await timeBlockRepository.findAllByUserId(userId);

      // Transform the MongoDB documents to ensure each has a proper id field
      return timeBlocks.map(block => timeBlockRepository.transformBlock(block));
    } catch (error) {
      logger.error('Fetch time blocks error:', error);
      throw ApiError.internal('Server error', ErrorCode.DATABASE_ERROR);
    }
  }

  /**
   * Get time blocks for a user by date
   * @param userId User ID
   * @param date Date string (YYYY-MM-DD)
   * @returns Array of time blocks
   */
  async getTimeBlocksByDate(userId: string, date: string) {
    try {
      const timeBlocks = await timeBlockRepository.findByUserIdAndDate(userId, date);

      // Transform the MongoDB documents to ensure each has a proper id field
      return timeBlocks.map(block => timeBlockRepository.transformBlock(block));
    } catch (error) {
      logger.error('Fetch time blocks by date error:', error);
      throw ApiError.internal('Server error', ErrorCode.DATABASE_ERROR);
    }
  }

  /**
   * Create a new time block
   * @param userId User ID
   * @param data TimeBlock data
   * @returns Created time block
   */
  async createTimeBlock(userId: string, data: CreateTimeBlockRequest) {
    try {
      // Verify category exists and belongs to user
      const category = await categoryRepository.findById(data.category);
      if (!category) {
        throw ApiError.badRequest('Category not found', ErrorCode.CATEGORY_NOT_FOUND);
      }

      if (category.userId.toString() !== userId) {
        throw ApiError.forbidden('Category does not belong to user', ErrorCode.FORBIDDEN);
      }

      // Split start and end time into hours and minutes
      const [startHour, startMinute] = data.startTime.split(':').map(Number);
      const [endHour, endMinute] = data.endTime.split(':').map(Number);

      // Calculate total minutes for start and end
      const startTotalMinutes = startHour * 60 + startMinute;
      let endTotalMinutes = endHour * 60 + endMinute;

      // Handle cases where end time is 00:00 next day
      if (endHour === 0 && endMinute === 0) {
        endTotalMinutes = 24 * 60;
      }

      // Calculate the number of hour blocks needed
      const hourBlocks = [];
      let currentHour = startHour;
      let currentMinute = startMinute;

      while (currentHour * 60 + currentMinute < endTotalMinutes) {
        // Calculate end time for this block
        let blockEndHour = currentHour + 1;
        let blockEndMinute = currentMinute;

        // If this would exceed the overall end time, use the end time instead
        if (blockEndHour * 60 + blockEndMinute > endTotalMinutes) {
          blockEndHour = endHour;
          blockEndMinute = endMinute;
        }

        // Format times as strings
        const blockStartTime = `${String(currentHour).padStart(2, '0')}:${String(currentMinute).padStart(2, '0')}`;
        const blockEndTime = `${String(blockEndHour).padStart(2, '0')}:${String(blockEndMinute).padStart(2, '0')}`;

        // Create block data
        const blockData = {
          ...data,
          startTime: blockStartTime,
          endTime: blockEndTime
        };

        // Create the time block
        const timeBlock = await timeBlockRepository.create(userId, blockData);
        hourBlocks.push(timeBlockRepository.transformBlock(timeBlock));

        // Move to next hour
        currentHour = blockEndHour;
        currentMinute = blockEndMinute;
      }

      // Return the array of created blocks
      return hourBlocks.length === 1 ? hourBlocks[0] : hourBlocks;
    } catch (error) {
      logger.error('Create time block error:', error);

      if (error instanceof ApiError) {
        throw error;
      }

      throw ApiError.internal('Server error', ErrorCode.DATABASE_ERROR);
    }
  }

  /**
   * Update a time block
   * @param id TimeBlock ID
   * @param userId User ID
   * @param data TimeBlock data
   * @returns Updated time block
   */
  async updateTimeBlock(id: string, userId: string, data: UpdateTimeBlockRequest) {
    try {
      // Verify time block exists and belongs to user
      const existingTimeBlock = await timeBlockRepository.findById(id);
      if (!existingTimeBlock) {
        throw ApiError.notFound('Time block not found', ErrorCode.TIMEBLOCK_NOT_FOUND);
      }

      if (existingTimeBlock.userId.toString() !== userId) {
        throw ApiError.forbidden('Time block does not belong to user', ErrorCode.FORBIDDEN);
      }

      // If category is being updated, verify it exists and belongs to user
      if (data.category) {
        const category = await categoryRepository.findById(data.category);
        if (!category) {
          throw ApiError.badRequest('Category not found', ErrorCode.CATEGORY_NOT_FOUND);
        }

        if (category.userId.toString() !== userId) {
          throw ApiError.forbidden('Category does not belong to user', ErrorCode.FORBIDDEN);
        }
      }

      const timeBlock = await timeBlockRepository.update(id, data);
      if (!timeBlock) {
        throw ApiError.notFound('Time block not found', ErrorCode.TIMEBLOCK_NOT_FOUND);
      }

      // Transform the time block to ensure it has a proper id field and categoryData
      return timeBlockRepository.transformBlock(timeBlock);
    } catch (error) {
      logger.error('Update time block error:', error);

      if (error instanceof ApiError) {
        throw error;
      }

      throw ApiError.internal('Server error', ErrorCode.DATABASE_ERROR);
    }
  }

  /**
   * Delete a time block
   * @param id TimeBlock ID
   * @param userId User ID
   * @returns Success or error
   */
  async deleteTimeBlock(id: string, userId: string) {
    try {
      // Verify time block exists and belongs to user
      const existingTimeBlock = await timeBlockRepository.findById(id);
      if (!existingTimeBlock) {
        throw ApiError.notFound('Time block not found', ErrorCode.TIMEBLOCK_NOT_FOUND);
      }

      if (existingTimeBlock.userId.toString() !== userId) {
        throw ApiError.forbidden('Time block does not belong to user', ErrorCode.FORBIDDEN);
      }

      await timeBlockRepository.delete(id);
      return { success: true };
    } catch (error) {
      logger.error('Delete time block error:', error);

      if (error instanceof ApiError) {
        throw error;
      }

      throw ApiError.internal('Server error', ErrorCode.DATABASE_ERROR);
    }
  }

  /**
   * Ensure that todos for today's routines are created if they don't exist.
   * @param userId User ID
   */
  async ensureRoutineTodosForToday(userId: string): Promise<void> {
    logger.info(`Starting to ensure routine todos for today for userId: ${userId}`);
    try {
      const todayDateString = format(new Date(), 'yyyy-MM-dd');
      const currentDayOfWeek = new Date().getDay(); // Sunday: 0, Monday: 1, ...

      logger.info(`Today's date: ${todayDateString}, Day of week: ${currentDayOfWeek}`);

      // First, check if the user has any routines at all
      const allUserRoutines = await routineRepository.findAllByUserId(userId);

      if (allUserRoutines.length === 0) {
        logger.info(`User ${userId} has no routines. Skipping routine to todo conversion.`);
        return;
      }

      logger.info(`Fetched ${allUserRoutines.length} routines for userId: ${userId}`);

      // Get all existing todos for today
      const existingTodos = await timeBlockRepository.findByUserIdAndDate(userId, todayDateString);

      // Create a map of existing routine todos by routineId
      const existingRoutineTodoMap = new Map();
      existingTodos.forEach(todo => {
        if (todo.isTodo && todo.routineId) {
          const routineIdStr = todo.routineId.toString();
          existingRoutineTodoMap.set(routineIdStr, todo);
        }
      });

      logger.info(`Found ${existingRoutineTodoMap.size} existing routine todos for today`);

      // Filter routines that are scheduled for today and don't have todos yet
      const applicableRoutines = allUserRoutines.filter(routine => {
        const isToday = routine.days.includes(currentDayOfWeek);
        const alreadyCreated = existingRoutineTodoMap.has(routine.id);

        if (!isToday) {
          logger.debug(`Routine ${routine.id} (${routine.title}) is not scheduled for today (day ${currentDayOfWeek}). Days: ${routine.days}`);
        }
        if (alreadyCreated) {
          logger.debug(`Routine ${routine.id} (${routine.title}) already has a todo for today.`);
        }

        return isToday && !alreadyCreated;
      });

      logger.info(`Found ${applicableRoutines.length} applicable routines for userId: ${userId} for today that need todos.`);

      // Create todos for applicable routines
      for (const routine of applicableRoutines) {
        logger.debug(`Processing routine: ${routine.id} (${routine.title}) for userId: ${userId}`);

        // Double-check that this routine doesn't already have a todo
        // This is a safety check to prevent duplicates
        const existingTodo = await timeBlockRepository.findOneByCriteria({
          userId: userId,
          date: todayDateString,
          routineId: routine.id,
          isTodo: true
        });

        if (existingTodo) {
          logger.info(`Todo for routine ${routine.id} (${routine.title}) already exists for today. Skipping creation.`);
          continue;
        }

        const note = `[Routine] ${routine.title}${routine.note ? `: ${routine.note}` : ''}`;

        logger.info(`Creating todo for routine ${routine.id} (${routine.title}).`);
        const timeBlockData: CreateTimeBlockRequest = {
          date: todayDateString,
          startTime: routine.startTime,
          endTime: routine.endTime,
          note: note,
          category: routine.categoryId,
          isTodo: true,
          isCompleted: false,
          routineId: routine.id,
        };

        try {
          await this.createTimeBlock(userId, timeBlockData);
          logger.info(`Successfully created todo for routine ${routine.id} (${routine.title}) for userId: ${userId}`);
        } catch (error) {
          logger.error(`Error creating todo for routine ${routine.id} (${routine.title}):`, error);
        }
      }

      logger.info(`Finished ensuring routine todos for today for userId: ${userId}`);
    } catch (error) {
      logger.error(`Error in ensureRoutineTodosForToday for userId ${userId}:`, error);
    }
  }
}
