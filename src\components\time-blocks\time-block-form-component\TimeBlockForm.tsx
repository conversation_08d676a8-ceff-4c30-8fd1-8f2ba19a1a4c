"use client"

import { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import * as z from 'zod';
import { toast } from 'sonner';
import { Dialog, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { CustomDialogContent } from '@/components/ui/custom-dialog';
import { Form, FormField, FormItem, FormLabel } from '@/components/ui/form';
import { Button } from '@/components/ui/button';
import { TimeBlock, TimeBlockFormData } from '@/lib/types';
import { parse } from 'date-fns';
import { format } from 'date-fns';
import { Calendar } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { CalendarIcon } from 'lucide-react';
import { cn } from '@/lib/utils';
import { TimeInputs } from './TimeInputs';
import { CategoryPicker } from './CategoryPicker';
import { TodoSwitch } from './TodoSwitch';
import { NoteInput } from './NoteInput';
import { useCategories } from '@/hooks/use-categories';

// Schema moved to a separate validation file
const timeBlockSchema = z.object({
  date: z.date({
    required_error: "Please select a date",
  }),
  startTime: z.string().regex(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/, {
    message: "Start time must be in 24-hour format (HH:MM)",
  }),
  endTime: z.string().regex(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/, {
    message: "End time must be in 24-hour format (HH:MM)",
  }),
  note: z.string().min(1, { message: "Note is required" }),
  category: z.string(), // Category is required in TimeBlockFormData
  isTodo: z.boolean().default(false),
}).refine(data => {
  const start = data.startTime.split(':').map(Number);
  const end = data.endTime.split(':').map(Number);
  const startMinutes = start[0] * 60 + start[1];
  const endMinutes = end[0] * 60 + end[1];
  return endMinutes > startMinutes;
}, {
  message: "End time must be after start time",
  path: ["endTime"],
});

interface TimeBlockFormProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (data: TimeBlockFormData) => Promise<boolean>;
  timeBlock?: TimeBlock;
  selectedDate: Date;
}

export function TimeBlockForm({ isOpen, onClose, onSubmit, timeBlock, selectedDate }: TimeBlockFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [newCategoryId, setNewCategoryId] = useState<string | null>(null);
  const { categories, createCategory } = useCategories();

  // Get current time in HH:00 format
  const getCurrentTime = () => {
    const now = new Date();
    return `${String(now.getHours()).padStart(2, '0')}:00`;
  };

  // Get time 1 hour later in HH:00 format
  const getOneHourLater = () => {
    const now = new Date();
    now.setHours(now.getHours() + 1);
    return `${String(now.getHours()).padStart(2, '0')}:00`;
  };

  const form = useForm<z.infer<typeof timeBlockSchema>>({
    resolver: zodResolver(timeBlockSchema),
    defaultValues: {
      date: selectedDate,
      startTime: getCurrentTime(),
      endTime: getOneHourLater(),
      note: '',
      category: categories.length > 0 ? categories[0].id : '',
      isTodo: false,
    },
  });

  // Update form's default category when categories change or when a new category is created
  useEffect(() => {
    if (categories.length > 0) {
      // If we have a new category ID, select it
      if (newCategoryId && categories.some(cat => cat.id === newCategoryId)) {
        form.setValue('category', newCategoryId, { shouldValidate: true });
        setNewCategoryId(null);
      }
      // Otherwise, if no category is selected, select the first one
      else if (!form.getValues('category') && !timeBlock) {
        form.setValue('category', categories[0].id, { shouldValidate: true });
      }
    } else if (categories.length === 0 && !timeBlock) {
      // If no categories exist, clear the category field
      form.setValue('category', '', { shouldValidate: false });
    }
  }, [categories, form, timeBlock, newCategoryId]);

  // Handle creating a new category from the form
  const handleCreateCategory = async (name: string, color: string) => {
    try {
      const newCategory = await createCategory(name, color);
      if (newCategory?.id) {
        setNewCategoryId(newCategory.id);
        return newCategory;
      }
    } catch (error) {
      console.error('Failed to create category:', error);
      throw error;
    }
  };

  // Use useEffect to reset form values when timeBlock changes
  useEffect(() => {
    if (timeBlock) {
      // Editing an existing time block
      let categoryValue = '';
      if (typeof timeBlock.category === 'string') {
        categoryValue = timeBlock.category;
      } else if (timeBlock.category && typeof timeBlock.category === 'object') {
        // If category is an object, extract the ID (_id from Mongoose, id from transformed)
        categoryValue = (timeBlock.category as any)._id || (timeBlock.category as any).id || '';
      }

      form.reset({
        date: timeBlock.date ? parse(timeBlock.date, 'yyyy-MM-dd', new Date()) : selectedDate,
        startTime: timeBlock.startTime,
        endTime: timeBlock.endTime,
        note: timeBlock.note,
        category: categoryValue,
        isTodo: !!timeBlock.isTodo, // Ensure boolean
      });
    } else {
      // Creating a new time block or resetting after an edit if dialog stays open
      form.reset({
        date: selectedDate,
        startTime: getCurrentTime(),
        endTime: getOneHourLater(),
        note: '',
        category: categories.length > 0 ? categories[0].id : '',
        isTodo: false,
      });
    }
  }, [timeBlock, selectedDate, categories, form, form.reset]);

  const handleSubmit = async (values: z.infer<typeof timeBlockSchema>) => {
    setIsSubmitting(true);
    try {
      // Handle the case when no categories exist - create a default one
      if (categories.length === 0 || !values.category || values.category === '') {
        if (categories.length === 0) {
          // Create a default category for new users
          try {
            const defaultCategory = await handleCreateCategory("General", "#3b82f6");
            if (defaultCategory && defaultCategory.id) {
              values.category = defaultCategory.id;
              toast.success("Created default category 'General'");
            } else {
              toast.error("Failed to create default category. Please create a category first.");
              setIsSubmitting(false);
              return;
            }
          } catch (error) {
            console.error("Error creating default category:", error);
            toast.error("Failed to create default category. Please create a category first.");
            setIsSubmitting(false);
            return;
          }
        } else {
          toast.error("Please select a category");
          setIsSubmitting(false);
          return;
        }
      }

      // Validate that the category exists in the user's categories
      const categoryExists = categories.some(cat => cat.id === values.category);

      if (!categoryExists && categories.length > 0) {
        // If the category doesn't exist but we have categories, use the first one
        values.category = categories[0].id;
        toast.info("Selected category was invalid. Using default category instead.");
      }

      const dataToSubmit = {
        ...values,
        date: format(values.date, 'yyyy-MM-dd'),
        isTodo: values.isTodo !== undefined ? values.isTodo : false,
      };

      console.log('Submitting data:', dataToSubmit);
      const success = await onSubmit(dataToSubmit);

      if (success) {
        // Only reset the form if we're not editing
        if (!timeBlock) {
          form.reset({
            date: selectedDate,
            startTime: getCurrentTime(),
            endTime: getOneHourLater(),
            note: '',
            category: categories.length > 0 ? categories[0].id : '',
            isTodo: false,
          });
        }
        handleDialogClose();
      }
    } catch (error) {
      // Only log errors in development
      if (process.env.NODE_ENV === 'development') {
        console.error('Error submitting time block:', error);
      }
      toast.error(error instanceof Error ? error.message : "Failed to create time block");
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle dialog close
  const handleDialogClose = () => {
    // Always reset the form when closing
    form.reset({
      date: selectedDate,
      startTime: getCurrentTime(),
      endTime: getOneHourLater(),
      note: '',
      category: categories.length > 0 ? categories[0].id : '',
      isTodo: false,
    });
    onClose();
  };

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && handleDialogClose()}>
      <CustomDialogContent className="w-[90vw] max-w-md p-4 rounded-lg">
        <DialogHeader className="pb-2">
          <div className="flex items-center justify-between">
            <DialogTitle className="text-lg font-semibold">
              {timeBlock ? 'Edit Time Block' : 'Add Time Block'}
            </DialogTitle>
            <div className="text-sm text-muted-foreground">
              {format(selectedDate, 'EEE, MMM d, yyyy')}
            </div>
          </div>
        </DialogHeader>

        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-3 mt-2">
            {/* Date Picker */}
            <FormField
              control={form.control}
              name="date"
              render={({ field }) => (
                <FormItem className="flex flex-col">
                  <FormLabel>Date</FormLabel>
                  <Popover>
                    <PopoverTrigger asChild>
                      <Button
                        variant={"outline"}
                        className={cn(
                          "w-full pl-3 text-left font-normal",
                          !field.value && "text-muted-foreground"
                        )}
                      >
                        {field.value ? (
                          format(field.value, "PPP")
                        ) : (
                          <span>Pick a date</span>
                        )}
                        <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                      </Button>
                    </PopoverTrigger>
                    <PopoverContent className="w-auto p-0" align="start">
                      <Calendar
                        mode="single"
                        selected={field.value}
                        onSelect={field.onChange}
                        disabled={(date) =>
                          date < new Date("1900-01-01")
                        }
                        initialFocus
                      />
                    </PopoverContent>
                  </Popover>
                </FormItem>
              )}
            />

            <TimeInputs form={form} />
            <CategoryPicker
            form={form}
            onCreateCategory={handleCreateCategory}
          />
            <TodoSwitch form={form} />
            <NoteInput form={form} />

            {/* Action Buttons */}
            <div className="flex gap-3 pt-2">
              <Button
                type="button"
                variant="outline"
                onClick={handleDialogClose}
                className="flex-1 h-10 text-sm border-input"
              >
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={isSubmitting}
                className="flex-1 h-10 text-sm"
              >
                {isSubmitting ? 'Saving...' : timeBlock ? 'Update' : 'Add'}
              </Button>
            </div>
          </form>
        </Form>
      </CustomDialogContent>
    </Dialog>
  );
}
