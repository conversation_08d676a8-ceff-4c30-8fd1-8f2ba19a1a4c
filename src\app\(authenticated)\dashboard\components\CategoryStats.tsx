"use client"

import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { cn } from '@/lib/utils';

interface CategoryStat {
  categoryId: string;
  category: string;
  color: string;
  minutes: number;
  hours: number;
  remainingMinutes: number;
}

interface CategoryStatsProps {
  categoryStats: CategoryStat[];
  totalMinutes: number;
  getTextColor: (bgColor: string) => string;
}

export function CategoryStats({ categoryStats, totalMinutes, getTextColor }: CategoryStatsProps) {
  return (
    <Card className="shadow-sm border border-border/60 hover:border-border/80 transition-colors h-full">
      <CardContent className="p-4">
        <h3 className="text-base font-semibold mb-4">Time by Category</h3>
        {categoryStats.length > 0 ? (
          <div className="space-y-4">
            {categoryStats.map((stat) => (
              <div key={stat.categoryId} className="space-y-1.5">
                <div className="flex justify-between items-center">
                  <div className="flex items-center gap-2">
                    <div
                      className="w-3.5 h-3.5 rounded-full" style={{ backgroundColor: stat.color }} // Dynamic color, must use inline style as Tailwind cannot generate dynamic classes
                    ></div>
                    <span className="text-sm font-medium">{stat.category}</span>
                  </div>
                  <Badge
                    variant="outline"
                    className="font-medium text-xs py-0.5 px-2 h-6"
                  >
                    {stat.hours > 0 ? `${stat.hours}h` : ''}
                    {stat.remainingMinutes > 0 ? `${stat.remainingMinutes}m` : stat.hours === 0 ? '0m' : ''}
                  </Badge>
                </div>
                <div className="w-full bg-muted/30 rounded-full h-2 overflow-hidden">
                  <div
                    className="h-full rounded-full"
                    style={{
                      width: `${Math.round((stat.minutes / totalMinutes) * 100)}%`, // Dynamic width, must use inline style as Tailwind cannot generate dynamic classes
                      backgroundColor: stat.color,
                    }}
                  ></div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-2 text-muted-foreground text-xs">
            No time tracked for this period
          </div>
        )}
      </CardContent>
    </Card>
  );
}
