"use client"

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Dialog, DialogTitle } from "@/components/ui/dialog";
import { CustomDialogContent } from "@/components/ui/custom-dialog";
import { Pencil, Trash2, X, Clock, Calendar } from "lucide-react";
import { FormRoutine } from "@/types/routine";
import { Badge } from "@/components/ui/badge";
import { VisuallyHidden } from "@/components/ui/visually-hidden";

interface RoutineDetailsProps {
  routine: FormRoutine;
  isOpen: boolean;
  onClose: () => void;
  onEdit: (routine: FormRoutine) => void;
  onDelete: (id: string) => void;
}

const WEEKDAYS = ["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"];

export function RoutineDetails({
  routine,
  isOpen,
  onClose,
  onEdit,
  onDelete,
}: RoutineDetailsProps) {
  // Calculate duration in hours
  const timeToMinutes = (time: string) => {
    const [hours, minutes] = time.split(":").map(Number);
    return hours * 60 + minutes;
  };

  const getDuration = (startTime: string, endTime: string) => {
    const startMinutes = timeToMinutes(startTime);
    const endMinutes = timeToMinutes(endTime);
    return endMinutes - startMinutes;
  };

  const duration = getDuration(routine.startTime, routine.endTime);
  const durationInHours = Math.round((duration / 60) * 10) / 10;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <CustomDialogContent className="w-[95vw] max-w-md p-0 rounded-lg bg-white dark:bg-zinc-900 shadow-xl overflow-hidden">
        <DialogTitle>
          <VisuallyHidden>Routine Details: {routine.title}</VisuallyHidden>
        </DialogTitle>
        {/* Header with color from category */}
        <div
          className="p-3 flex items-center justify-between"
          style={{
            borderBottom: `2px solid ${routine.categoryData?.color || '#888'}`,
            background: `${routine.categoryData?.color || '#888'}10`
          }}
        >
          <div className="flex items-center gap-2">
            <div
              className="w-5 h-5 rounded-full"
              style={{ backgroundColor: routine.categoryData?.color || '#888' }}
            />
            <h2 className="text-base font-semibold truncate">{routine.title}</h2>
          </div>
          <div className="flex items-center gap-1">
            <Button
              variant="ghost"
              size="icon"
              className="h-7 w-7 rounded-full"
              onClick={() => onEdit(routine)}
              aria-label="Edit routine"
            >
              <Pencil className="h-3.5 w-3.5" />
            </Button>
            <Button
              variant="ghost"
              size="icon"
              className="h-7 w-7 rounded-full text-destructive"
              onClick={() => onDelete(routine.id || '')}
              aria-label="Delete routine"
            >
              <Trash2 className="h-3.5 w-3.5" />
            </Button>
            <Button
              variant="ghost"
              size="icon"
              className="h-7 w-7 rounded-full"
              onClick={onClose}
              aria-label="Close details"
            >
              <X className="h-3.5 w-3.5" />
            </Button>
          </div>
        </div>

        {/* Content */}
        <div className="p-4 space-y-3">
          {/* Time */}
          <div className="flex items-center gap-2">
            <Clock className="h-3.5 w-3.5 text-muted-foreground" />
            <div className="flex items-center gap-1.5">
              <span className="text-sm font-medium">{routine.startTime} - {routine.endTime}</span>
              <span className="text-xs text-muted-foreground">({durationInHours}h)</span>
            </div>
          </div>

          {/* Days */}
          <div className="flex items-start gap-2">
            <Calendar className="h-3.5 w-3.5 text-muted-foreground mt-0.5" />
            <div className="flex flex-wrap gap-1.5">
              {routine.days.map((day) => (
                <Badge
                  key={day}
                  variant="outline"
                  className="px-2 py-0.5 text-xs font-medium"
                >
                  {WEEKDAYS[day]}
                </Badge>
              ))}
            </div>
          </div>

          {/* Category */}
          <div className="flex items-center gap-2">
            <div className="flex items-center gap-1.5">
              <div
                className="w-3.5 h-3.5 rounded-full"
                style={{ backgroundColor: routine.categoryData?.color || "#888" }}
              />
              <span className="text-sm">
                {routine.categoryData?.name || "Uncategorized"}
              </span>
            </div>
          </div>

          {/* Notes */}
          {routine.note && (
            <div className="mt-3 pt-3 border-t">
              <p className="text-sm text-muted-foreground whitespace-pre-wrap">
                {routine.note}
              </p>
            </div>
          )}
        </div>
      </CustomDialogContent>
    </Dialog>
  );
}
