"use client";

import { useState, useEffect, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { clearAllAuthData, validateUserData } from '@/utils/auth-cleanup';

// Cache the auth status in memory
let cachedAuth = {
  isAuthenticated: false,
  lastChecked: 0
};

const AUTH_CACHE_TIME = 5 * 60 * 1000; // 5 minutes

export function useAuth({ redirectTo }: { redirectTo?: string } = {}) {
  const [isAuthenticated, setIsAuthenticated] = useState(cachedAuth.isAuthenticated);
  const [isLoading, setIsLoading] = useState(true);
  const router = useRouter();

  const handleAuthFailure = useCallback(async () => {
    console.log('Handling authentication failure - clearing all data');

    // Clear all corrupted data
    clearAllAuthData();

    // Try to clear the server-side cookie by calling logout API
    try {
      await fetch('/api/auth/logout', {
        method: 'POST',
        credentials: 'include',
      });
      console.log('Server-side logout completed');
    } catch (error) {
      console.warn('Failed to call logout API:', error);
    }

    // Update state
    cachedAuth = { isAuthenticated: false, lastChecked: Date.now() };
    setIsAuthenticated(false);
    setIsLoading(false);

    // Force redirect to root page instead of login to avoid loops
    if (redirectTo) {
      console.log('Redirecting to root page due to auth failure');
      // Use window.location for more reliable redirect
      window.location.href = '/';
    }
  }, [redirectTo]);

  const checkAuth = useCallback(async () => {
    const now = Date.now();

    // Return cached value if valid
    if (now - cachedAuth.lastChecked < AUTH_CACHE_TIME) {
      console.log('Using cached auth result:', cachedAuth.isAuthenticated);
      setIsAuthenticated(cachedAuth.isAuthenticated);
      setIsLoading(false);
      return;
    }

    console.log('Checking authentication with API...');
    try {
      // Check the API to verify the cookie as the source of truth
      const response = await fetch('/api/auth/profile', {
        method: 'GET',
        credentials: 'include',
      });

      console.log('Auth API response status:', response.status);

      if (response.ok) {
        const userData = await response.json();

        // Validate the response data
        if (!validateUserData(userData)) {
          console.warn('Invalid user data received from API');
          handleAuthFailure();
          return;
        }

        try {
          localStorage.setItem('user', JSON.stringify({
            ...userData.data || userData,
            lastLogin: new Date().toISOString()
          }));
        } catch (storageError) {
          console.warn('Failed to store user data:', storageError);
          // Continue anyway, don't fail authentication for storage issues
        }

        cachedAuth = { isAuthenticated: true, lastChecked: now };
        setIsAuthenticated(true);
      } else {
        // Check if it's a "User not found" error (404)
        if (response.status === 404) {
          console.warn('User not found in database - token exists but user was deleted');
          handleAuthFailure();
          return;
        }

        // For other API errors, also clear everything and redirect
        console.warn(`API returned error: ${response.status}`);
        handleAuthFailure();
        return;
      }
    } catch (error) {
      console.warn('Auth API call failed:', error);

      // Try localStorage as fallback if network request fails
      try {
        const userStr = localStorage.getItem('user');
        if (userStr) {
          const user = JSON.parse(userStr);

          // Validate localStorage data
          if (validateUserData({ data: user })) {
            cachedAuth = { isAuthenticated: true, lastChecked: now };
            setIsAuthenticated(true);
          } else {
            console.warn('Invalid user data in localStorage');
            handleAuthFailure();
            return;
          }
        } else {
          handleAuthFailure();
          return;
        }
      } catch (localStorageError) {
        console.warn('localStorage access failed:', localStorageError);
        handleAuthFailure();
        return;
      }
    } finally {
      setIsLoading(false);
    }
  }, [redirectTo, router, handleAuthFailure]);

  useEffect(() => {
    checkAuth();
  }, [checkAuth]);

  const logout = async () => {
    try {
      // Call logout API to clear the cookie
      await fetch('/api/auth/logout', {
        method: 'POST',
        credentials: 'include',
      });

      // Clear all authentication data
      clearAllAuthData();

      // Update authentication state
      cachedAuth = { isAuthenticated: false, lastChecked: Date.now() };
      setIsAuthenticated(false);

      // Redirect to root page
      router.push('/');
    } catch (error) {
      // Even if API call fails, clear all data
      clearAllAuthData();

      // Update authentication state
      cachedAuth = { isAuthenticated: false, lastChecked: Date.now() };
      setIsAuthenticated(false);

      // Redirect to root page
      router.push('/');
    }
  };

  return {
    isAuthenticated,
    isLoading,
    logout,
    checkAuth
  };
}
