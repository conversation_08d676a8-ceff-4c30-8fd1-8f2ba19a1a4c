"use client"

import { ToggleGroup, ToggleGroupItem } from "@/components/ui/toggle-group";
import { FormControl, FormField, FormItem, FormLabel } from "@/components/ui/form";
import { UseFormReturn } from "react-hook-form";

const WEEKDAYS = [
  { value: "0", label: "Su" },
  { value: "1", label: "Mo" },
  { value: "2", label: "Tu" },
  { value: "3", label: "We" },
  { value: "4", label: "Th" },
  { value: "5", label: "Fr" },
  { value: "6", label: "Sa" },
];

interface WeekdayPickerProps {
  form: UseFormReturn<any>;
}

export function WeekdayPicker({ form }: WeekdayPickerProps) {
  return (
    <FormField
      control={form.control}
      name="days"
      render={({ field }) => (
        <FormItem>
          <FormLabel>Days</FormLabel>
          <FormControl>
            <ToggleGroup
              type="multiple"
              variant="outline"
              className="justify-start"
              value={field.value?.map(String)}
              onValueChange={(value) => {
                field.onChange(value.map(Number));
              }}
            >
              {WEEKDAYS.map((day) => (
                <ToggleGroupItem
                  key={day.value}
                  value={day.value}
                  variant="outline"
                  className="w-10 px-0"
                >
                  {day.label}
                </ToggleGroupItem>
              ))}
            </ToggleGroup>
          </FormControl>
        </FormItem>
      )}
    />
  );
}
