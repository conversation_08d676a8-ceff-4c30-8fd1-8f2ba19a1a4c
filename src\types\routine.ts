export type Category = {
  name: string;
  color: string;
};

// Base routine interface with common properties
export interface BaseRoutine {
  title: string;
  startTime: string;
  endTime: string;
  days: number[];
  note?: string;
  categoryData?: Category;
}

// API data format
export interface ApiRoutine extends BaseRoutine {
  id: string;
  categoryId: string;
}

// Form data format
export interface FormRoutine extends BaseRoutine {
  id?: string;
  categoryId: string;
}

// Type guard to check if a routine is API format
export function isApiRoutine(routine: any): routine is ApiRoutine {
  return 'categoryId' in routine;
}

// Conversion functions between API and form formats
export function apiToFormRoutine(apiRoutine: ApiRoutine): FormRoutine {
  const { ...rest } = apiRoutine;
  return {
    ...rest
  };
}

export function formToApiRoutine(formRoutine: FormRoutine): ApiRoutine {
  const { id, ...rest } = formRoutine;
  return {
    ...rest,
    id: id || '',
  };
}
