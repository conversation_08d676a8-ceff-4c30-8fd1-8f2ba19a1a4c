/**
 * Test script for database connection and user authentication
 * 
 * Run this script with:
 * node -r dotenv/config src/scripts/test-auth.js
 */

const mongoose = require('mongoose');
const bcrypt = require('bcrypt');

// MongoDB connection string
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/time-tracker-app';

// Connect to MongoDB
async function connectDB() {
  try {
    console.log('Connecting to MongoDB...');
    console.log('MongoDB URI:', MONGODB_URI.substring(0, 20) + '...');
    
    await mongoose.connect(MONGODB_URI);
    console.log('Connected to MongoDB successfully');
    return true;
  } catch (error) {
    console.error('MongoDB connection error:', error);
    return false;
  }
}

// Test user authentication
async function testUserAuth(email, password) {
  try {
    console.log(`Testing authentication for user: ${email}`);
    
    // Get User model
    const User = mongoose.model('User');
    
    // Find user by email
    console.log('Finding user by email...');
    const user = await User.findOne({ email });
    
    if (!user) {
      console.log(`User with email ${email} not found`);
      return false;
    }
    
    console.log(`User found: ${user.name}, ID: ${user._id}`);
    
    // Compare password
    console.log('Comparing password...');
    console.log(`Stored password hash length: ${user.password.length}`);
    
    const isMatch = await bcrypt.compare(password, user.password);
    console.log(`Password match result: ${isMatch}`);
    
    return isMatch;
  } catch (error) {
    console.error('Authentication test error:', error);
    return false;
  }
}

// List all users in the database
async function listUsers() {
  try {
    console.log('Listing all users in the database...');
    
    // Get User model
    const User = mongoose.model('User');
    
    // Find all users
    const users = await User.find({}).select('name email');
    
    console.log(`Found ${users.length} users:`);
    users.forEach(user => {
      console.log(`- ${user.name} (${user.email})`);
    });
    
    return users;
  } catch (error) {
    console.error('Error listing users:', error);
    return [];
  }
}

// Main function
async function main() {
  try {
    // Connect to database
    const connected = await connectDB();
    if (!connected) {
      console.error('Failed to connect to database. Exiting...');
      process.exit(1);
    }
    
    // List all users
    await listUsers();
    
    // Test authentication with provided credentials
    const email = process.argv[2];
    const password = process.argv[3];
    
    if (email && password) {
      console.log('\nTesting authentication with provided credentials...');
      const authenticated = await testUserAuth(email, password);
      
      if (authenticated) {
        console.log('\n✅ Authentication successful!');
      } else {
        console.log('\n❌ Authentication failed!');
      }
    } else {
      console.log('\nNo credentials provided. Skipping authentication test.');
      console.log('To test authentication, run:');
      console.log('node -r dotenv/config src/scripts/test-auth.js <email> <password>');
    }
  } catch (error) {
    console.error('Error in main function:', error);
  } finally {
    // Close database connection
    await mongoose.disconnect();
    console.log('\nDisconnected from MongoDB');
  }
}

// Run the main function
main();
