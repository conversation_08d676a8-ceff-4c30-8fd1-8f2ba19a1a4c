import { NextResponse } from 'next/server';

/**
 * Standard success response
 * @param data Response data
 * @param status HTTP status code
 * @returns NextResponse with standardized format
 */
export function successResponse<T>(data: T, status: number = 200): NextResponse {
  const response = NextResponse.json(
    {
      success: true,
      data
    },
    {
      status,
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0'
      }
    }
  );

  return response;
}

/**
 * Standard error response
 * @param message Error message
 * @param code Error code
 * @param status HTTP status code
 * @returns NextResponse with standardized format
 */
export function errorResponse(message: string, code: string, status: number = 500): NextResponse {
  return NextResponse.json(
    {
      success: false,
      error: {
        code,
        message
      }
    },
    {
      status,
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'Pragma': 'no-cache',
        'Expires': '0'
      }
    }
  );
}

/**
 * Created response (201)
 * @param data Response data
 * @returns NextResponse with 201 status
 */
export function createdResponse<T>(data: T): NextResponse {
  return successResponse(data, 201);
}

/**
 * No content response (204)
 * @returns NextResponse with 204 status
 */
export function noContentResponse(): NextResponse {
  return new NextResponse(null, {
    status: 204,
    headers: {
      'Cache-Control': 'no-cache, no-store, must-revalidate',
      'Pragma': 'no-cache',
      'Expires': '0'
    }
  });
}
