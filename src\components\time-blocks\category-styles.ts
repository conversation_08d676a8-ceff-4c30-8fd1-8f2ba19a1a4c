import { Category, TimeBlock } from "@/lib/types";

// Generate category-specific class names for dynamic styling
export function getCategoryColorClass(color: string, type: 'bg' | 'text' | 'border', opacity?: number) {
  // Create safe class name from color by removing '#' and converting to lowercase
  const safeColor = color.replace('#', '').toLowerCase();
  const opacityStr = opacity ? `-${opacity}` : '';

  return `${type}-[${color}]${opacityStr}`;
}

// Generate styles for category elements as a string of Tailwind classes
export function getCategoryStyles(color: string) {
  return {
    dot: `w-2 h-2 rounded-full flex-shrink-0 bg-[${color}]`,
    badge: `text-[10px] py-0 px-1 h-4 rounded-full bg-[${color}20] text-[${color}] border-[${color}40] border`,
    button: `bg-[${color}] hover:bg-[${color}] hover:opacity-90 transition-opacity`,
    indicator: `w-4 h-4 rounded-full bg-[${color}]`,
    border: `border-[${color}]`,
    text: `text-[${color}]`,
    background: `bg-[${color}]`,
    backgroundLight: `bg-[${color}20]`,
    borderColor: color, // Add the raw color value for dynamic styling
  };
}

// Get the full color object for a TimeBlock
export function getTimeBlockCategoryStyles(block: TimeBlock, categories: Category[]) {
  const color = getTimeBlockCategoryColor(block, categories);
  return getCategoryStyles(color);
}

// Get the color for a TimeBlock's category
export function getTimeBlockCategoryColor(block: TimeBlock, categories: Category[]) {
  if (block.categoryData?.color) {
    return block.categoryData.color;
  }

  const categoryId = typeof block.category === 'object' && block.category !== null
    ? (block.category as any).id || block.category
    : block.category;

  const category = categories.find((c) => c.id === categoryId);
  return category?.color || "#6b7280";
}
