"use client"

import { format } from 'date-fns';
import { Plus, BetweenHorizontalStart, Tag } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { TabsList, TabsTrigger } from '@/components/ui/tabs';

interface HomeHeaderProps {
  selectedDate: Date;
  onAddTimeBlock: () => void;
  activeTab: string;
}

export function HomeHeader({ selectedDate, onAddTimeBlock, activeTab }: HomeHeaderProps) {
  return (
    <div className="sticky top-0 z-50 bg-background border-b border-border/40 w-full">
      <div className="w-full px-3 py-2">
        {/* Mobile: Date and Add Note Button Row */}
        <div className="flex justify-between items-center sm:hidden">
          <div className="flex items-center space-x-1.5">
            <h2 className="text-base font-semibold tracking-tight">
              {format(selectedDate, "EEE, MMM d")}
            </h2>
            <span className="text-base font-semibold tracking-tight">
              {format(selectedDate, "yyyy")}
            </span>
          </div>

          <Button
            onClick={onAddTimeBlock}
            size="sm"
            className="h-8 bg-primary hover:bg-primary/90 text-primary-foreground rounded-full px-3 shadow-sm"
          >
            <Plus className="h-3.5 w-3.5 mr-1" />
            <span className="text-xs font-medium">Add Note</span>
          </Button>
        </div>

        {/* Desktop: All components in one line */}
        <div className="hidden sm:flex justify-between items-center">
          <div className="flex items-center space-x-2">
            <h2 className="text-lg font-semibold tracking-tight">
              {format(selectedDate, "EEEE, MMMM d")}
            </h2>
            <span className="text-lg font-semibold tracking-tight">
              {format(selectedDate, "yyyy")}
            </span>
          </div>

          <div className="mx-4">
            <TabsList className="h-8 bg-muted/50 p-0.5 rounded-lg">
              <TabsTrigger
                value="grid"
                className="px-3 h-7 rounded-md data-[state=active]:bg-background data-[state=active]:shadow-sm transition-all duration-200"
              >
                <BetweenHorizontalStart className="h-3.5 w-3.5 mr-1.5" />
                <span className="text-sm font-medium">Grid View</span>
              </TabsTrigger>
              <TabsTrigger
                value="list"
                className="px-3 h-7 rounded-md data-[state=active]:bg-background data-[state=active]:shadow-sm transition-all duration-200"
              >
                <Tag className="h-3.5 w-3.5 mr-1.5" />
                <span className="text-sm font-medium">My Todos</span>
              </TabsTrigger>
            </TabsList>
          </div>

          <Button
            onClick={onAddTimeBlock}
            size="sm"
            className="h-8 bg-primary hover:bg-primary/90 text-primary-foreground rounded-full px-4 shadow-sm"
          >
            <Plus className="h-3.5 w-3.5 mr-1" />
            <span className="text-sm font-medium">Add Note</span>
          </Button>
        </div>

        {/* Mobile: Tab Navigation */}
        <div className="sm:hidden w-full mt-2">
          <TabsList className="w-full flex bg-muted/50 p-0.5 rounded-lg h-8">
            <TabsTrigger
              value="grid"
              className="flex-1 min-w-[60px] h-7 rounded-md data-[state=active]:bg-background data-[state=active]:shadow-sm transition-all duration-200"
            >
              <BetweenHorizontalStart className="h-3.5 w-3.5 mr-1" />
              <span className="text-sm font-medium">Grid</span>
            </TabsTrigger>
            <TabsTrigger
              value="list"
              className="flex-1 min-w-[60px] h-7 rounded-md data-[state=active]:bg-background data-[state=active]:shadow-sm transition-all duration-200"
            >
              <Tag className="h-3.5 w-3.5 mr-1" />
              <span className="text-sm font-medium">Todos</span>
            </TabsTrigger>
          </TabsList>
        </div>
      </div>
    </div>
  );
}
