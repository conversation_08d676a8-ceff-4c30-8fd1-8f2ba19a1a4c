import mongoose from 'mongoose';
import { timeBlockSchema } from '@/db/schemas/timeblock.schema';

/**
 * TimeBlock document interface
 */
export interface ITimeBlock extends mongoose.Document {
  userId: mongoose.Types.ObjectId;
  date: string;
  startTime: string;
  endTime: string;
  note: string;
  category: mongoose.Types.ObjectId | {
    name: string;
    color: string;
  };
  categoryData?: {
    name: string;
    color: string;
  };
  routineId?: mongoose.Types.ObjectId;
  isTodo: boolean;
  isCompleted: boolean;
}

/**
 * TimeBlock model
 */
const TimeBlock = (mongoose.models.TimeBlock || mongoose.model<ITimeBlock>('TimeBlock', timeBlockSchema));

export default TimeBlock;
