"use client"

import { motion } from 'framer-motion';
import { But<PERSON> } from '@/components/ui/button';
import { Circle, CheckCircle2 } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { useCategories } from '@/hooks/use-categories';
import { TimeBlock } from '@/lib/types';
import { usePreferences } from '@/hooks/use-preferences';
import { cn } from '@/lib/utils';

interface TimeBlockCardProps {
  block: TimeBlock;
  onUpdate: (id: string, data: Partial<TimeBlock>) => void;
  onEdit: (timeBlock: TimeBlock) => void;
  onClick: () => void;
}

export function TimeBlockCard({
  block,
  onUpdate,
  onClick
}: TimeBlockCardProps) {
  const { categories } = useCategories();
  const { formatTime } = usePreferences();

  // Get category color
  const getCategoryColor = (block: TimeBlock) => {
    if (block.categoryData?.color) {
      return block.categoryData.color;
    }

    const categoryId = typeof block.category === 'object' && block.category !== null
      ? (block.category as any).id || block.category
      : block.category;

    const category = categories.find((c) => c.id === categoryId);
    return category?.color || "#6b7280";
  };

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.95 }}
      animate={{ opacity: 1, scale: 1 }}
      className={cn(
        "rounded-md relative group flex-shrink-0 border-2 cursor-pointer overflow-hidden",
        "w-[190px] sm:w-[195px] md:w-[220px] h-[calc(100%-2px)] flex flex-col",
        block.isTodo && block.isCompleted
          ? "bg-green-100 dark:bg-green-900/30 border-green-400"
          : undefined
      )}
      style={
        block.isTodo && block.isCompleted
          ? { borderColor: "rgba(34,197,94,0.6)" }
          : { backgroundColor: getCategoryColor(block) + '20', borderColor: getCategoryColor(block) + '99' } // Dynamic color, must use inline style as Tailwind cannot generate dynamic classes
      }
      onClick={onClick}
    >
      <div
        className={cn(
          "w-full card-top-bar h-[2px]",
          block.isTodo && block.isCompleted
            ? "bg-green-500"
            : undefined
        )}
        style={
          block.isTodo && block.isCompleted
            ? { backgroundColor: "rgb(34,197,94)" }
            : { backgroundColor: getCategoryColor(block) } // Dynamic color, must use inline style as Tailwind cannot generate dynamic classes
        }
      />
      <div className="flex justify-between items-center my-1 h-[30px] px-1">
        <div className="flex items-center gap-1">
          {block.isTodo && (
            <Button
              variant="ghost"
              size="icon"
              className={cn(
                "h-7 w-7 rounded-full p-0 mr-1",
                block.isCompleted
                  ? "text-green-600 border-2 border-green-500 bg-green-100 dark:bg-green-900/40"
                  : "text-muted-foreground hover:text-primary border-2 border-muted-foreground/50 bg-background/80"
              )}
              onClick={(e) => {
                e.stopPropagation();
                if (!block || !block.id) return;
                onUpdate(block.id, { isCompleted: !block.isCompleted });
              }}
            >
              {block.isCompleted ? (
                <CheckCircle2 className="h-5 w-5" />
              ) : (
                <Circle className="h-5 w-5" />
              )}
              <span className="sr-only">
                {block.isCompleted ? "Mark as incomplete" : "Mark as complete"}
              </span>
            </Button>
          )}
        </div>

        {/* Centered time text */}
        <div className="text-[14px] font-medium text-foreground text-center w-full">
          {formatTime(block.startTime)} - {formatTime(block.endTime)}
        </div>
      </div>
      <div className="flex-grow px-1 pb-1 h-[calc(100%-31px-30px)]">
        <div className="text-xs break-words whitespace-normal leading-snug overflow-y-auto h-full scrollbar-thin scrollbar-thumb-rounded scrollbar-thumb-muted-foreground/20 scrollbar-track-transparent">
          {block.note}
        </div>
      </div>
    </motion.div>
  );
}
