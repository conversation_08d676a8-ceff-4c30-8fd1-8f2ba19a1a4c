"use client"

import { useState, useEffect } from 'react';
import { UseFormReturn, useFieldArray } from 'react-hook-form';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { FormField, FormItem, FormLabel, FormControl, FormDescription, FormMessage } from '@/components/ui/form';
import { Switch } from '@/components/ui/switch';
import { Card, CardContent } from '@/components/ui/card';
import { Plus, Trash2 } from 'lucide-react';
import { PreferencesFormValues } from '../../schemas';

interface CustomTimeBlocksPreferenceProps {
  form: UseFormReturn<PreferencesFormValues>;
}

export function CustomTimeBlocksPreference({ form }: CustomTimeBlocksPreferenceProps) {
  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: "customTimeBlocks",
  });

  const useCustomTimeBlocks = form.watch("useCustomTimeBlocks");
  const customTimeBlocks = form.watch("customTimeBlocks") || [];

  const addTimeBlock = () => {
    // Find the last end time or use 09:00 as default
    const lastBlock = customTimeBlocks[customTimeBlocks.length - 1];
    const defaultStartTime = lastBlock ? lastBlock.endTime : "09:00";
    
    // Calculate default end time (1 hour after start time)
    const [startHour, startMinute] = defaultStartTime.split(':').map(Number);
    const endMinutes = (startHour * 60 + startMinute + 60) % (24 * 60);
    const endHour = Math.floor(endMinutes / 60);
    const endMinute = endMinutes % 60;
    const defaultEndTime = `${endHour.toString().padStart(2, '0')}:${endMinute.toString().padStart(2, '0')}`;
    
    append({ startTime: defaultStartTime, endTime: defaultEndTime });
  };

  const removeTimeBlock = (index: number) => {
    remove(index);
  };

  // Validate time blocks for overlaps
  const validateTimeBlocks = () => {
    if (!customTimeBlocks || customTimeBlocks.length <= 1) return true;

    const sortedBlocks = [...customTimeBlocks].sort((a, b) => {
      const aStart = a.startTime.split(':').map(Number);
      const bStart = b.startTime.split(':').map(Number);
      return (aStart[0] * 60 + aStart[1]) - (bStart[0] * 60 + bStart[1]);
    });

    for (let i = 0; i < sortedBlocks.length - 1; i++) {
      const current = sortedBlocks[i];
      const next = sortedBlocks[i + 1];
      
      const [currentEndHour, currentEndMinute] = current.endTime.split(':').map(Number);
      const [nextStartHour, nextStartMinute] = next.startTime.split(':').map(Number);
      
      const currentEndMinutes = currentEndHour * 60 + currentEndMinute;
      const nextStartMinutes = nextStartHour * 60 + nextStartMinute;
      
      // Handle overnight blocks
      const adjustedNextStart = nextStartMinutes < currentEndMinutes ? nextStartMinutes + 24 * 60 : nextStartMinutes;
      
      if (currentEndMinutes > adjustedNextStart) {
        form.setError(`customTimeBlocks.${i}.endTime`, {
          type: 'manual',
          message: 'Time blocks cannot overlap'
        });
        form.setError(`customTimeBlocks.${i + 1}.startTime`, {
          type: 'manual',
          message: 'Time blocks cannot overlap'
        });
        return false;
      }
    }
    return true;
  };

  // Watch for changes to validate
  useEffect(() => {
    if (useCustomTimeBlocks && customTimeBlocks.length > 0) {
      validateTimeBlocks();
    }
  }, [customTimeBlocks, useCustomTimeBlocks]);

  return (
    <div className="bg-muted/10 p-3 rounded-md">
      <div className="space-y-4">
        {/* Toggle for using custom time blocks */}
        <FormField
          control={form.control}
          name="useCustomTimeBlocks"
          render={({ field }) => (
            <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3">
              <div className="space-y-0.5">
                <FormLabel className="text-sm font-medium">
                  Use Custom Time Blocks
                </FormLabel>
                <FormDescription className="text-xs">
                  No default time intervals, Show in grid view custom time blocks.
                </FormDescription>
              </div>
              <FormControl>
                <Switch
                  checked={field.value}
                  onCheckedChange={field.onChange}
                />
              </FormControl>
            </FormItem>
          )}
        />

        {/* Custom time blocks section */}
        {useCustomTimeBlocks && (
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <h4 className="text-sm font-medium">Custom Time Blocks</h4>
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={addTimeBlock}
                className="h-8"
              >
                <Plus className="h-4 w-4 mr-1" />
                Add Time Block
              </Button>
            </div>

            {fields.length === 0 && (
              <div className="text-center py-4 text-sm text-muted-foreground border rounded-md">
                No time blocks added. Click "Add Time Block" to create your first block.
              </div>
            )}

            <div className="space-y-2">
              {fields.map((field, index) => (
                <Card key={field.id} className="p-3">
                  <CardContent className="p-0">
                    <div className="flex items-center gap-3">
                      <div className="flex-1 grid grid-cols-2 gap-2">
                        <FormField
                          control={form.control}
                          name={`customTimeBlocks.${index}.startTime`}
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel className="text-xs">Start Time</FormLabel>
                              <FormControl>
                                <Input
                                  type="time"
                                  {...field}
                                  className="h-8 text-sm"
                                  onChange={(e) => {
                                    field.onChange(e);
                                    validateTimeBlocks();
                                  }}
                                />
                              </FormControl>
                              <FormMessage className="text-xs" />
                            </FormItem>
                          )}
                        />
                        <FormField
                          control={form.control}
                          name={`customTimeBlocks.${index}.endTime`}
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel className="text-xs">End Time</FormLabel>
                              <FormControl>
                                <Input
                                  type="time"
                                  {...field}
                                  className="h-8 text-sm"
                                  onChange={(e) => {
                                    field.onChange(e);
                                    validateTimeBlocks();
                                  }}
                                />
                              </FormControl>
                              <FormMessage className="text-xs" />
                            </FormItem>
                          )}
                        />
                      </div>
                      <Button
                        type="button"
                        variant="outline"
                        size="sm"
                        onClick={() => removeTimeBlock(index)}
                        className="h-8 w-8 p-0 text-destructive hover:text-destructive"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>

            {fields.length > 0 && (
              <FormDescription className="text-xs">
                Custom time blocks will be displayed in the grid view instead of regular intervals.
               
              </FormDescription>
            )}
          </div>
        )}
      </div>
    </div>
  );
}
