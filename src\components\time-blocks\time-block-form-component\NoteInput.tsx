"use client"

import { FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Textarea } from '@/components/ui/textarea';
import { UseFormReturn } from 'react-hook-form';

interface NoteInputProps {
  form: UseFormReturn<any>;
}

export function NoteInput({ form }: NoteInputProps) {
  return (
    <FormField
      control={form.control}
      name="note"
      render={({ field }) => (
        <FormItem>
          <FormLabel className="text-sm font-medium">Note</FormLabel>
          <FormControl>
            <Textarea
              placeholder="What did you do during this time?"
              className="min-h-[100px] text-sm resize-none border-input focus-visible:ring-1"
              {...field}
            />
          </FormControl>
          <FormMessage className="text-xs" />
        </FormItem>
      )}
    />
  );
}
