/**
 * Authentication configuration
 */

// Environment variables
export const JWT_SECRET = process.env.JWT_SECRET || 'your-secret-key';
export const JWT_EXPIRY = '7d'; // Changed from '1d' to '7d' to match cookie expiry

// Cookie options
export const COOKIE_OPTIONS = {
  httpOnly: true,
  secure: process.env.NODE_ENV === 'production',
  sameSite: 'lax' as const, // Changed from 'strict' to 'lax' for better PWA compatibility
  path: '/',
  maxAge: 7 * 24 * 60 * 60 * 1000, // 7 days (extended for better user experience)
};
