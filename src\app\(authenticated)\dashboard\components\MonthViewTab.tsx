"use client"

import { format, isSameDay, isToday, parseISO, startOfMonth, endOfMonth, eachDayOfInterval, isSameMonth, addDays } from 'date-fns';
import { Card, CardContent } from '@/components/ui/card';
import { TimeBlock } from '@/lib/types';
import { cn } from '@/lib/utils';
import { usePreferences } from '@/hooks/use-preferences';

interface MonthViewTabProps {
  loading: boolean;
  selectedDate: Date;
  timeBlocks: TimeBlock[] | undefined;
  setSelectedDate: (date: Date) => void;
  setBlockToEdit: (block: TimeBlock) => void;
  setIsFormOpen: (isOpen: boolean) => void;
  getCategoryColor: (category: string | object, opacity?: number) => string;
  getTextColor: (bgColor: string) => string;
}

export function MonthViewTab({
  loading,
  selectedDate,
  timeBlocks,
  setSelectedDate,
  setBlockToEdit,
  setIsFormOpen,
  getCategoryColor,
  getTextColor
}: MonthViewTabProps) {
  // Get user preferences for time formatting
  const { formatTime } = usePreferences();
  // Generate month view grid
  const renderMonthView = () => {
    // Get the first day of the month
    const monthStart = startOfMonth(selectedDate);

    // Generate dates for the selected day
    const selectedDay = selectedDate;

    // Show all 24 hours for better visibility
    const hours = Array.from({ length: 24 }, (_, i) => i);

    return (
      <div className="relative">
        {/* Month header with selected date */}
        <div className="text-center py-2 font-medium sticky top-0 bg-background z-30 border-b">
          <div className={cn(
            "text-base flex items-center justify-center gap-2",
            isToday(selectedDay) && "text-primary"
          )}>
            <span>{format(selectedDay, 'd MMMM yyyy')}</span>
            <span className="text-sm text-muted-foreground">{format(selectedDay, 'EEEE')}</span>
          </div>
        </div>

        {/* Month calendar for date selection - Compact version */}
        <div className="border-b bg-background sticky top-[52px] z-20">
          <div className="grid grid-cols-7 gap-1 p-2 max-w-md mx-auto">
            {/* Day name headers */}
            {['M', 'T', 'W', 'T', 'F', 'S', 'S'].map((day, i) => (
              <div key={`day-${i}`} className="text-center text-xs font-medium">
                {day}
              </div>
            ))}

            {/* Empty cells before the start of the month */}
            {Array.from({ length: monthStart.getDay() === 0 ? 6 : monthStart.getDay() - 1 }).map((_, i) => (
              <div key={`empty-start-${i}`} className="h-7 w-7"></div>
            ))}

            {/* Month days */}
            {eachDayOfInterval({
              start: monthStart,
              end: endOfMonth(selectedDate)
            }).map((date, i) => {
              const hasEvents = timeBlocks?.some(block => {
                try {
                  const blockDate = parseISO(block.date);
                  return isSameDay(blockDate, date);
                } catch (error) {
                  return false;
                }
              });

              return (
                <div
                  key={`date-${i}`}
                  className={cn(
                    "h-7 w-7 rounded-full flex items-center justify-center text-xs cursor-pointer hover:bg-primary/20 relative",
                    isSameDay(date, selectedDate) && "bg-primary text-primary-foreground",
                    isToday(date) && !isSameDay(date, selectedDate) && "border border-primary text-primary"
                  )}
                  onClick={() => setSelectedDate(date)}
                >
                  {format(date, 'd')}
                  {hasEvents && (
                    <div className="absolute bottom-0.5 w-1 h-1 rounded-full bg-primary"></div>
                  )}
                </div>
              );
            })}
          </div>
        </div>

        {/* Time grid */}
        <div className="relative">
          {hours.map((hour) => {
            // Get blocks for this hour
            const blocksForHour = timeBlocks?.filter(block => {
              try {
                const blockDate = parseISO(block.date);
                if (!isSameDay(blockDate, selectedDay)) return false;

                const [startHour] = block.startTime.split(':').map(Number);
                const [endHour] = block.endTime.split(':').map(Number);

                // Include blocks that overlap with this hour
                return (startHour <= hour && endHour > hour) ||
                       (startHour === hour);
              } catch (error) {
                console.error("Error processing block:", error);
                return false;
              }
            }) || [];

            // Determine if this is a current hour (for highlighting)
            const now = new Date();
            const isCurrentHour = isToday(selectedDay) && now.getHours() === hour;

            return (
              <div key={hour} className="grid grid-cols-[65px_1fr] border-b">
                {/* Hour label - fixed on scroll */}
                <div className={cn(
                  "sticky left-0 bg-background z-10 py-2 px-2.5 text-sm flex items-center justify-end border-r",
                  isCurrentHour ? "text-primary font-medium" : "text-muted-foreground"
                )}>
                  {formatTime(`${hour.toString().padStart(2, '0')}:00`)}
                </div>

                {/* Day column */}
                <div
                  className={cn(
                    "p-1.5 border-r min-h-[70px] relative",
                    isCurrentHour && "bg-primary/5"
                  )}
                >
                  {blocksForHour.length === 0 ? (
                    // Empty cell - clickable to add new block
                    <div
                      className="w-full h-full min-h-[67px] cursor-pointer hover:bg-primary/5 rounded transition-colors"
                      onClick={() => {
                        setSelectedDate(selectedDay);
                        setIsFormOpen(true);
                      }}
                    ></div>
                  ) : (
                    // Render blocks for this hour
                    blocksForHour.map((block, i) => (
                      <div
                        key={i}
                        className="text-sm p-2 rounded mb-1 shadow-sm cursor-pointer overflow-hidden hover:shadow-md transition-shadow relative"
                        style={{
                          backgroundColor: getCategoryColor(block.category, 0.85),
                          color: getTextColor(getCategoryColor(block.category)),
                          borderLeft: `3px solid ${getCategoryColor(block.category)}`
                        }}
                        onClick={() => {
                          // Set the selected date to this block's date
                          const blockDate = parseISO(block.date);
                          setSelectedDate(blockDate);
                          setBlockToEdit(block);
                          setIsFormOpen(true);
                        }}
                      >
                        <div className="font-medium">{formatTime(block.startTime)} - {formatTime(block.endTime)}</div>
                        <div className="line-clamp-2 text-xs mt-1">{block.note}</div>
                      </div>
                    ))
                  )}
                </div>
              </div>
            );
          })}
        </div>
      </div>
    );
  };

  return (
    <Card className="shadow-sm overflow-hidden border border-border/60 hover:border-border/80 transition-colors">
      <CardContent className="p-0">
        {loading ? (
          <div className="flex flex-col items-center justify-center h-32 gap-2 p-4">
            <p className="text-xs text-muted-foreground">Loading time blocks...</p>
          </div>
        ) : (
          <div className="overflow-auto">
            <div className="min-w-[350px] sm:min-w-[500px] md:min-w-[700px] lg:min-w-[900px]">
              {renderMonthView()}
            </div>
            <div className="h-2"></div> {/* Add some bottom padding */}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
