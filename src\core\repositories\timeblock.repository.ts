import TimeBlock, { ITimeBlock } from '@/db/models/TimeBlock';
import connectDB from '@/config/database';
import { CreateTimeBlockRequest, UpdateTimeBlockRequest } from '@/types/timeblock';
import mongoose from 'mongoose';
import { ApiError } from '@/core/errors/api-error';
import { ErrorCode } from '@/constants/error-codes';
import logger from '@/utils/logger';

/**
 * TimeBlock repository for database operations
 */
export class TimeBlockRepository {
  /**
   * Find all time blocks for a user
   * @param userId User ID
   * @returns Array of time blocks
   */
  async findAllByUserId(userId: string): Promise<ITimeBlock[]> {
    try {
      await connectDB();
      return TimeBlock.find({ userId })
        .populate('category', 'name color')
        .sort({ date: -1, startTime: 1 });
    } catch (error) {
      logger.error('Find all time blocks error:', error);
      throw new ApiError(ErrorCode.DATABASE_ERROR, 'Database error', 500);
    }
  }

  /**
   * Find one time block by criteria
   * @param criteria Criteria to search by
   * @returns TimeBlock or null
   */
  async findOneByCriteria(criteria: Record<string, any>): Promise<ITimeBlock | null> {
    try {
      await connectDB();

      // Convert string IDs to ObjectIds where needed
      const processedCriteria = { ...criteria };

      // Handle userId conversion if it's a string
      if (processedCriteria.userId && typeof processedCriteria.userId === 'string') {
        processedCriteria.userId = new mongoose.Types.ObjectId(processedCriteria.userId);
      }

      // Handle routineId conversion if it's a string
      if (processedCriteria.routineId && typeof processedCriteria.routineId === 'string') {
        processedCriteria.routineId = new mongoose.Types.ObjectId(processedCriteria.routineId);
      }

      const timeBlock = await TimeBlock.findOne(processedCriteria).populate('category', 'name color');
      // No need to transform here as the service layer usually handles transformation if needed
      // If transformation is consistently needed by callers, this.transformBlock(timeBlock) could be used.
      return timeBlock;
    } catch (error) {
      logger.error('Find one time block by criteria error:', error);
      throw new ApiError(ErrorCode.DATABASE_ERROR, 'Database error', 500);
    }
  }

  /**
   * Find time blocks for a user by date
   * @param userId User ID
   * @param date Date string (YYYY-MM-DD)
   * @returns Array of time blocks
   */
  async findByUserIdAndDate(userId: string, date: string): Promise<ITimeBlock[]> {
    try {
      await connectDB();
      return TimeBlock.find({ userId, date })
        .populate('category', 'name color')
        .sort({ startTime: 1 });
    } catch (error) {
      logger.error('Find time blocks by date error:', error);
      throw new ApiError(ErrorCode.DATABASE_ERROR, 'Database error', 500);
    }
  }

  /**
   * Find time block by ID
   * @param id TimeBlock ID
   * @returns TimeBlock or null
   */
  async findById(id: string): Promise<ITimeBlock | null> {
    try {
      await connectDB();
      return TimeBlock.findById(id).populate('category', 'name color');
    } catch (error) {
      logger.error('Find time block by ID error:', error);
      throw new ApiError(ErrorCode.DATABASE_ERROR, 'Database error', 500);
    }
  }

  /**
   * Create new time block
   * @param userId User ID
   * @param data TimeBlock data
   * @returns Created time block
   */
  async create(userId: string, data: CreateTimeBlockRequest): Promise<ITimeBlock> {
    try {
      await connectDB();
      const timeBlock = new TimeBlock({
        userId,
        ...data
      });
      await timeBlock.save();
      return timeBlock.populate('category', 'name color');
    } catch (error) {
      logger.error('Create time block error:', error);

      if (error instanceof mongoose.Error.ValidationError) {
        throw new ApiError(ErrorCode.VALIDATION_ERROR, error.message, 400);
      }

      throw new ApiError(ErrorCode.DATABASE_ERROR, 'Database error', 500);
    }
  }

  /**
   * Update time block
   * @param id TimeBlock ID
   * @param data TimeBlock data
   * @returns Updated time block
   */
  async update(id: string, data: UpdateTimeBlockRequest): Promise<ITimeBlock | null> {
    try {
      await connectDB();
      return TimeBlock.findByIdAndUpdate(id, data, { new: true })
        .populate('category', 'name color');
    } catch (error) {
      logger.error('Update time block error:', error);

      if (error instanceof mongoose.Error.ValidationError) {
        throw new ApiError(ErrorCode.VALIDATION_ERROR, error.message, 400);
      }

      throw new ApiError(ErrorCode.DATABASE_ERROR, 'Database error', 500);
    }
  }

  /**
   * Delete time block
   * @param id TimeBlock ID
   * @returns Deleted time block
   */
  async delete(id: string): Promise<ITimeBlock | null> {
    try {
      await connectDB();
      return TimeBlock.findByIdAndDelete(id);
    } catch (error) {
      logger.error('Delete time block error:', error);
      throw new ApiError(ErrorCode.DATABASE_ERROR, 'Database error', 500);
    }
  }

  /**
   * Transform MongoDB document to plain object with string ID
   * @param block TimeBlock document
   * @returns Transformed time block
   */
  transformBlock(block: ITimeBlock) {
    const blockObj = block.toObject();

    // Extract category data if it was populated
    const categoryData = block.category && typeof block.category !== 'string' &&
      'name' in block.category && 'color' in block.category ? {
      name: block.category.name,
      color: block.category.color
    } : undefined;

    return {
      ...blockObj,
      id: (block._id as mongoose.Types.ObjectId).toString(),
      // Include categoryData in the transformed object
      categoryData: categoryData
    };
  }
}
