import { NextRequest, NextResponse } from "next/server";
import { routineSchema } from "@/db/schemas/routine.schema";
import connectDB from "@/config/database";
import { verifyAuth } from "@/utils/auth";
import mongoose from "mongoose";

export async function PUT(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Verify user is authenticated
    const { authenticated, user } = verifyAuth(req);
    if (!authenticated || !user) {
      return NextResponse.json({ message: "Unauthorized" }, { status: 401 });
    }

    // Parse and validate request body
    const body = await req.json();
    await connectDB();

    // Get category data if categoryId is being updated
    let categoryData = undefined;
    if (body.categoryId) {
      const category = await mongoose.connection
        .collection("categories")
        .findOne({ _id: new mongoose.Types.ObjectId(body.categoryId) });

      if (!category) {
        return NextResponse.json({ message: "Category not found" }, { status: 400 });
      }

      categoryData = {
        name: category.name,
        color: category.color
      };
    }

    // Validate the update data
    const validated = routineSchema.parse({
      ...body,
      userId: user.id,
      categoryId: body.categoryId || body.category, // Handle both keys for backward compatibility
    });

    // Prepare update data
    const updateData = {
      ...validated,
      categoryData,
      updatedAt: new Date(),
    };

    // Get the id from params promise
    const { id } = await params;

    // Update the routine
    const result = await mongoose.connection.collection("routines").findOneAndUpdate(
      {
        _id: new mongoose.Types.ObjectId(id),
        userId: user.id
      },
      { $set: updateData },
      { returnDocument: "after" }
    );

    if (!result) {
      return NextResponse.json(
        { message: "Routine not found" },
        { status: 404 }
      );
    }

    // Return updated routine
    return NextResponse.json({
      ...result,
      id: result._id.toString(),
      _id: undefined,
      categoryData
    });
  } catch (error) {
    console.error("Error updating routine:", error);
    if (error instanceof Error) {
      return NextResponse.json({ message: error.message }, { status: 400 });
    }
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    );
  }
}

export async function DELETE(
  req: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // Verify user is authenticated
    const { authenticated, user } = verifyAuth(req);
    if (!authenticated || !user) {
      return NextResponse.json({ message: "Unauthorized" }, { status: 401 });
    }

    await connectDB();

    // Get the id from params promise
    const { id } = await params;
    const routineId = new mongoose.Types.ObjectId(id);

    // Delete the routine
    const result = await mongoose.connection.collection("routines").findOneAndDelete({
      _id: routineId,
      userId: user.id,
    });

    if (!result) {
      return NextResponse.json(
        { message: "Routine not found" },
        { status: 404 }
      );
    }

    // Also delete any todos associated with this routine
    const deleteTodosResult = await mongoose.connection.collection("timeblocks").deleteMany({
      routineId: routineId,
      userId: new mongoose.Types.ObjectId(user.id),
    });

    return NextResponse.json({
      success: true,
      deletedTodos: deleteTodosResult.deletedCount || 0
    });
  } catch (error) {
    console.error("Error deleting routine:", error);
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    );
  }
}
