import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { errorResponse } from '@/utils/response';
import { ErrorCode } from '@/constants/error-codes';

// Create category request schema
export const createCategorySchema = z.object({
  name: z.string().min(1, 'Name is required'),
  color: z.string().regex(/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/, 'Color must be a valid hex code'),
});

// Update category request schema
export const updateCategorySchema = z.object({
  name: z.string().min(1, 'Name is required').optional(),
  color: z.string().regex(/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/, 'Color must be a valid hex code').optional(),
}).refine(data => Object.keys(data).length > 0, {
  message: 'At least one field must be provided',
  path: ['body'],
});

/**
 * Validate create category request
 */
export async function validateCreateCategory(req: NextRequest): Promise<{ data: z.infer<typeof createCategorySchema> } | NextResponse> {
  try {
    const body = await req.json();
    const result = createCategorySchema.safeParse(body);

    if (!result.success) {
      const errors = result.error.errors.map(err => `${err.path.join('.')}: ${err.message}`).join(', ');
      return errorResponse(errors, ErrorCode.VALIDATION_ERROR, 400);
    }

    return { data: result.data };
  } catch (error) {
    return errorResponse('Invalid request body', ErrorCode.BAD_REQUEST, 400);
  }
}

/**
 * Validate update category request
 */
export async function validateUpdateCategory(req: NextRequest): Promise<{ data: z.infer<typeof updateCategorySchema> } | NextResponse> {
  try {
    const body = await req.json();
    const result = updateCategorySchema.safeParse(body);

    if (!result.success) {
      const errors = result.error.errors.map(err => `${err.path.join('.')}: ${err.message}`).join(', ');
      return errorResponse(errors, ErrorCode.VALIDATION_ERROR, 400);
    }

    return { data: result.data };
  } catch (error) {
    return errorResponse('Invalid request body', ErrorCode.BAD_REQUEST, 400);
  }
}
