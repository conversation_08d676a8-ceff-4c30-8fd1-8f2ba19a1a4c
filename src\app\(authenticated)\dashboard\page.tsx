"use client"

import { useState, useEffect } from 'react';
import { addDays, startOfWeek, endOfWeek } from 'date-fns';
import { Ta<PERSON>, <PERSON><PERSON>Content, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { TimeBlock } from '@/lib/types';
import { TimeBlockForm } from '@/components/time-blocks/time-block-form';
import { useTimeBlocks } from '@/hooks/use-time-blocks';
import { useCategories } from '@/hooks/use-categories';
import { usePreferences } from '@/hooks/use-preferences';
import { toast } from 'sonner';
import {
  DateNavigation,
  AnalyticsTab,
  GridViewTab,
  WeekViewTab,
  TodoTab,
  MonthViewTab
} from './components';
import {
  AnalyticsPeriod,
  getPeriodDescription,
  getCategoryColor,
  getTextColor,
  filterTimeBlocksByDate,
  getBlocksForPeriod,
  calculateTotalTimeTracked,
  calculateCategoryStats
} from './utils/dashboardUtils';

export default function DashboardPage() {
  const { categories } = useCategories();
  const { preferences } = usePreferences();
  const [selectedDate, setSelectedDate] = useState<Date>(new Date());
  const [weekDates, setWeekDates] = useState<Date[]>([]);
  const [isFormOpen, setIsFormOpen] = useState(false);
  const [blockToEdit, setBlockToEdit] = useState<TimeBlock | undefined>(undefined);
  const { timeBlocks, loading, addTimeBlock, updateTimeBlock, deleteTimeBlock, refreshTimeBlocks } = useTimeBlocks();
  const [filteredBlocks, setFilteredBlocks] = useState<TimeBlock[]>([]);
  const [analyticsPeriod, setAnalyticsPeriod] = useState<AnalyticsPeriod>('daily');
  const [weeklyBlocks, setWeeklyBlocks] = useState<TimeBlock[]>([]);
  const [monthlyBlocks, setMonthlyBlocks] = useState<TimeBlock[]>([]);
  const [isRefreshing, setIsRefreshing] = useState<boolean>(false);

  // Generate week dates
  useEffect(() => {
    const start = startOfWeek(selectedDate, { weekStartsOn: 1 });
    const end = endOfWeek(selectedDate, { weekStartsOn: 1 });
    const dates: Date[] = [];

    let current = start;
    while (current <= end) {
      dates.push(new Date(current));
      current = addDays(current, 1);
    }

    setWeekDates(dates);
  }, [selectedDate]);

  // Filter time blocks based on selected date
  useEffect(() => {
    if (timeBlocks) {
      // Use the utility function to filter blocks
      const { dailyBlocks, weeklyBlocks: weekly, monthlyBlocks: monthly } = filterTimeBlocksByDate(timeBlocks, selectedDate);
      setFilteredBlocks(dailyBlocks);
      setWeeklyBlocks(weekly);
      setMonthlyBlocks(monthly);
    }
  }, [timeBlocks, selectedDate]);

  // State for analytics data
  const [currentPeriodBlocks, setCurrentPeriodBlocks] = useState<TimeBlock[]>([]);
  const [categoryStats, setCategoryStats] = useState<any[]>([]);
  const [totalTimeTracked, setTotalTimeTracked] = useState<number>(0);

  // Update blocks for the current analytics period when period or blocks change
  useEffect(() => {
    const blocks = getBlocksForPeriod(analyticsPeriod, filteredBlocks, weeklyBlocks, monthlyBlocks);
    setCurrentPeriodBlocks(blocks);
  }, [analyticsPeriod, filteredBlocks, weeklyBlocks, monthlyBlocks]);

  // Update category stats when current period blocks or categories change
  useEffect(() => {
    const stats = calculateCategoryStats(currentPeriodBlocks, categories);
    setCategoryStats(stats);
  }, [currentPeriodBlocks, categories]);

  // Update total time tracked when current period blocks change
  useEffect(() => {
    const total = calculateTotalTimeTracked(currentPeriodBlocks);
    setTotalTimeTracked(total);
  }, [currentPeriodBlocks]);

  // Navigate to previous/next week
  const navigateWeek = (direction: 'prev' | 'next') => {
    setSelectedDate(prevDate => {
      return direction === 'prev'
        ? addDays(prevDate, -7)
        : addDays(prevDate, 7);
    });
  };

  // Function to refresh data
  const handleRefresh = async () => {
    setIsRefreshing(true);
    try {
      await refreshTimeBlocks();
      // The useEffect hooks will automatically update the filtered blocks and analytics
    } catch (error) {
      toast.error('Failed to refresh data');
    } finally {
      setIsRefreshing(false);
    }
  };

  return (
    <div className="min-h-screen bg-background">
      {/* Main content container with minimal spacing */}
      <div className="px-2 sm:px-4 md:px-6 py-2 space-y-2">
        {/* Date Navigation and Tabs in a more compact layout */}
        <div className="space-y-1">
          {/* Date Navigation Component with improved mobile layout */}
          <div className="bg-card rounded-lg shadow-sm p-1.5 sm:p-2">
            <DateNavigation
              selectedDate={selectedDate}
              setSelectedDate={setSelectedDate}
              navigateWeek={navigateWeek}
              onAddTimeBlock={() => setIsFormOpen(true)}
            />
          </div>

          {/* Tabs with improved mobile responsiveness */}
          <div className="bg-card rounded-lg shadow-sm">
            <Tabs defaultValue="analytics" className="w-full" onValueChange={async (newTab) => {
              // Refresh data when switching tabs to ensure synchronization
              try {
                await refreshTimeBlocks();
              } catch (error) {
                // Silently handle refresh errors
              }
            }}>
              <TabsList className="w-full flex flex-wrap bg-background/95 h-9 p-0.5">
                <TabsTrigger 
                  value="analytics" 
                  className="flex-1 min-w-[60px] rounded-md text-sm h-8 data-[state=active]:bg-primary data-[state=active]:text-primary-foreground"
                >
                  <span className="hidden xs:inline">Analytics</span>
                  <span className="xs:hidden">Stats</span>
                </TabsTrigger>
                <TabsTrigger 
                  value="month" 
                  className="flex-1 min-w-[60px] rounded-md text-sm h-8 data-[state=active]:bg-primary data-[state=active]:text-primary-foreground"
                >
                  <span className="hidden xs:inline">Month View</span>
                  <span className="xs:hidden">Month</span>
                </TabsTrigger>
                <TabsTrigger 
                  value="week" 
                  className="flex-1 min-w-[60px] rounded-md text-sm h-8 data-[state=active]:bg-primary data-[state=active]:text-primary-foreground"
                >
                  <span className="hidden xs:inline">Week View</span>
                  <span className="xs:hidden">Week</span>
                </TabsTrigger>
                <TabsTrigger 
                  value="grid" 
                  className="flex-1 min-w-[60px] rounded-md text-sm h-8 data-[state=active]:bg-primary data-[state=active]:text-primary-foreground"
                >
                  <span className="hidden xs:inline">Grid View</span>
                  <span className="xs:hidden">Grid</span>
                </TabsTrigger>
                <TabsTrigger 
                  value="list" 
                  className="flex-1 min-w-[60px] rounded-md text-sm h-8 data-[state=active]:bg-primary data-[state=active]:text-primary-foreground"
                >
                  <span className="hidden xs:inline">Todo View</span>
                  <span className="xs:hidden">Todo</span>
                </TabsTrigger>
              </TabsList>

              {/* Tab content with improved spacing */}
              <div className="mt-2">
                <TabsContent value="analytics" className="mt-0">
                  <div className="bg-card rounded-lg shadow-sm p-2 sm:p-3">
                    <AnalyticsTab
                      analyticsPeriod={analyticsPeriod}
                      setAnalyticsPeriod={(period) => setAnalyticsPeriod(period)}
                      periodDescription={getPeriodDescription(analyticsPeriod, selectedDate)}
                      filteredBlocks={filteredBlocks}
                      weeklyBlocks={weeklyBlocks}
                      monthlyBlocks={monthlyBlocks}
                      totalTimeTracked={totalTimeTracked}
                      categoryStats={categoryStats}
                      getTextColor={getTextColor}
                      onRefresh={handleRefresh}
                      isRefreshing={isRefreshing}
                    />
                  </div>
                </TabsContent>

                <TabsContent value="month" className="mt-0">
                  <div className="bg-card rounded-lg shadow-sm p-2 sm:p-3">
                    <MonthViewTab
                      loading={loading}
                      selectedDate={selectedDate}
                      timeBlocks={timeBlocks}
                      setSelectedDate={setSelectedDate}
                      setBlockToEdit={setBlockToEdit}
                      setIsFormOpen={setIsFormOpen}
                      getCategoryColor={(category, opacity) => getCategoryColor(category, categories, opacity)}
                      getTextColor={getTextColor}
                    />
                  </div>
                </TabsContent>

                <TabsContent value="week" className="mt-0">
                  <div className="bg-card rounded-lg shadow-sm p-2 sm:p-3">
                    <WeekViewTab
                      loading={loading}
                      weekDates={weekDates}
                      selectedDate={selectedDate}
                      timeBlocks={timeBlocks}
                      setSelectedDate={setSelectedDate}
                      setBlockToEdit={setBlockToEdit}
                      setIsFormOpen={setIsFormOpen}
                      getCategoryColor={(category, opacity) => getCategoryColor(category, categories, opacity)}
                      getTextColor={getTextColor}
                    />
                  </div>
                </TabsContent>

                <TabsContent value="grid" className="mt-0">
                  <div className="bg-card rounded-lg shadow-sm p-2 sm:p-3">
                    <GridViewTab
                      selectedDate={selectedDate}
                      filteredBlocks={filteredBlocks}
                      onAddBlock={() => setIsFormOpen(true)}
                      onEditBlock={(block) => {
                        setBlockToEdit(block);
                        setIsFormOpen(true);
                      }}
                      onDeleteBlock={deleteTimeBlock}
                      refreshTimeBlocks={refreshTimeBlocks}
                      onUpdateBlock={updateTimeBlock}
                    />
                  </div>
                </TabsContent>

                <TabsContent value="list" className="mt-0">
                  <div className="bg-card rounded-lg shadow-sm p-2 sm:p-3">
                    <TodoTab
                      selectedDate={selectedDate}
                      filteredBlocks={filteredBlocks.filter(block => block.isTodo)}
                      loading={loading}
                      onAddBlock={() => setIsFormOpen(true)}
                      onEditBlock={(block) => {
                        setBlockToEdit(block);
                        setIsFormOpen(true);
                      }}
                      onDeleteBlock={deleteTimeBlock}
                      onUpdateBlock={updateTimeBlock}
                    />
                  </div>
                </TabsContent>
              </div>
            </Tabs>
          </div>
        </div>

        {/* Time Block Form */}
        <TimeBlockForm
          isOpen={isFormOpen}
          onClose={() => {
            setIsFormOpen(false);
            setBlockToEdit(undefined);
          }}
          onSubmit={async (data) => {
            try {
              if (blockToEdit) {
                const success = await updateTimeBlock(blockToEdit.id, data);
                if (success) {
                  await refreshTimeBlocks();
                  return true;
                }
                return false;
              } else {
                const newBlock = await addTimeBlock(data);
                if (newBlock) {
                  await refreshTimeBlocks();
                  return true;
                }
                return false;
              }
            } catch (error) {
              console.error('Error handling time block operation:', error);
              return false;
            }
          }}
          timeBlock={blockToEdit}
          selectedDate={selectedDate}
        />
      </div>
    </div>
  );
}
