import { AuthService } from './auth.service';
import { TimeBlockService } from './timeblock.service';
import { CategoryService } from './category.service';

// Export singleton instances
export const authService = new AuthService();
export const timeBlockService = new TimeBlockService();
export const categoryService = new CategoryService();

// Export service classes
export { AuthService, TimeBlockService, CategoryService };
