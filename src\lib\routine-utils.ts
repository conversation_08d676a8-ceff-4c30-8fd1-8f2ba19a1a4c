/**
 * Utility functions for working with routines
 */
import { ApiRoutine } from "@/types/routine";
import { TimeBlock, TimeBlockFormData } from "@/types/timeblock";
import { v4 as uuidv4 } from 'uuid';

/**
 * Check if a routine is scheduled for a specific day of the week
 * @param routine The routine to check
 * @param dayOfWeek Day of week (0-6, where 0 is Sunday)
 * @returns boolean indicating if routine is scheduled for this day
 */
export function isRoutineScheduledForDay(routine: ApiRoutine, dayOfWeek: number): boolean {
  return routine.days.includes(dayOfWeek);
}

/**
 * Convert a routine to a todo time block
 * @param routine The routine to convert
 * @param date The date to set for the time block (ISO string format)
 * @returns A time block object representing the routine as a todo
 */
export function routineToTimeBlock(routine: ApiRoutine, date: string): TimeBlockFormData {
  return {
    date,
    startTime: routine.startTime,
    endTime: routine.endTime,
    note: `[Routine] ${routine.title}${routine.note ? `: ${routine.note}` : ''}`,
    category: routine.categoryId,
    isTodo: true,
    isCompleted: false,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
  };
}

/**
 * Check if a time block was created from a routine
 * @param timeBlock The time block to check
 * @returns boolean indicating if the time block was created from a routine
 */
export function isTimeBlockFromRoutine(timeBlock: TimeBlock): boolean {
  return timeBlock.note.startsWith('[Routine]');
}

/**
 * Get today's routines from the list of all routines
 * @param routines List of all routines
 * @returns Routines scheduled for today
 */
export function getTodayRoutines(routines: ApiRoutine[]): ApiRoutine[] {
  const today = new Date();
  const dayOfWeek = today.getDay(); // 0-6, where 0 is Sunday
  
  return routines.filter(routine => isRoutineScheduledForDay(routine, dayOfWeek));
}

/**
 * Convert today's routines to time blocks
 * @param routines List of all routines
 * @returns Time blocks created from today's routines
 */
export function convertTodayRoutinesToTimeBlocks(routines: ApiRoutine[]): TimeBlockFormData[] {
  const todayRoutines = getTodayRoutines(routines);
  const today = new Date().toISOString().split('T')[0]; // YYYY-MM-DD
  
  return todayRoutines.map(routine => routineToTimeBlock(routine, today));
}
