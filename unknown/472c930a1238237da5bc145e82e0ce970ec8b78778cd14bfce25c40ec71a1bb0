import mongoose from 'mongoose';
import { categorySchema } from '@/db/schemas/category.schema';

/**
 * Category document interface
 */
export interface ICategory extends mongoose.Document {
  userId: mongoose.Types.ObjectId;
  name: string;
  color: string;
}

/**
 * Category model
 */
const Category = (mongoose.models.Category || mongoose.model<ICategory>('Category', categorySchema));

export default Category;
