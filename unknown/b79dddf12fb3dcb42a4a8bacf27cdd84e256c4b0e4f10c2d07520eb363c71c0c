import mongoose from 'mongoose';

const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/time-tracker-app';

// Define the type for our cached mongoose connection
interface MongooseCache {
  conn: typeof mongoose | null;
  promise: Promise<typeof mongoose> | null;
}

// Add mongoose to NodeJS.Global interface
declare global {
  var mongoose: MongooseCache | undefined;
}

// Initialize the cached connection object
const cached: MongooseCache = global.mongoose || { conn: null, promise: null };

// Save the cached connection to the global object
if (!global.mongoose) {
  global.mongoose = cached;
}

/**
 * Connect to MongoDB database
 * @returns Mongoose connection
 */
async function connectDB() {
  // If we have a connection, return it
  if (cached.conn) {
    console.log('Using existing MongoDB connection');
    return cached.conn;
  }

  // If we don't have a promise yet, create one
  if (!cached.promise) {
    const opts = {
      bufferCommands: false,
    };

    console.log('Connecting to MongoDB with URI:', MONGODB_URI.substring(0, 20) + '...');

    cached.promise = mongoose.connect(MONGODB_URI, opts)
      .then((mongoose) => {
        console.log('Connected to MongoDB successfully');
        return mongoose;
      })
      .catch((error) => {
        console.error('MongoDB connection error:', error);
        // Log more details about the error
        if (error.name === 'MongoServerSelectionError') {
          console.error('Could not connect to MongoDB server. Please check:');
          console.error('1. MongoDB connection string is correct');
          console.error('2. Network connectivity to MongoDB server');
          console.error('3. MongoDB server is running');
        }
        throw error;
      });
  }

  try {
    // Wait for the promise to resolve and save the connection
    console.log('Waiting for MongoDB connection to be established...');
    cached.conn = await cached.promise;
    console.log('MongoDB connection established and cached');
    return cached.conn;
  } catch (error) {
    console.error('Error while waiting for MongoDB connection:', error);
    throw error;
  }
}

export default connectDB;
