/**
 * Direct database connection test script
 * Run with: node src/scripts/check-db.js
 */

const mongoose = require('mongoose');

// MongoDB connection string - update this with your actual connection string
const MONGODB_URI = process.env.MONGODB_URI || 'mongodb://localhost:27017/time-tracker-app';

async function testConnection() {
  try {
    console.log('Attempting to connect to MongoDB...');
    console.log(`Connection string: ${MONGODB_URI}`);
    
    await mongoose.connect(MONGODB_URI);
    console.log('✅ Successfully connected to MongoDB!');
    
    // Check if we can list collections
    console.log('\nListing collections:');
    const collections = await mongoose.connection.db.listCollections().toArray();
    
    if (collections.length === 0) {
      console.log('No collections found. Database might be empty.');
    } else {
      collections.forEach(collection => {
        console.log(`- ${collection.name}`);
      });
    }
    
    // Try to count documents in the users collection
    try {
      const usersCount = await mongoose.connection.db.collection('users').countDocuments();
      console.log(`\nFound ${usersCount} documents in the users collection`);
      
      if (usersCount > 0) {
        // Get a sample user (without sensitive data)
        const sampleUser = await mongoose.connection.db.collection('users')
          .findOne({}, { projection: { password: 0 } });
        
        console.log('\nSample user:');
        console.log(JSON.stringify(sampleUser, null, 2));
      }
    } catch (err) {
      console.log('\nCould not access users collection:', err.message);
    }
    
  } catch (error) {
    console.error('❌ MongoDB connection error:', error);
  } finally {
    // Close the connection
    if (mongoose.connection.readyState !== 0) {
      await mongoose.disconnect();
      console.log('\nDisconnected from MongoDB');
    }
  }
}

// Run the test
testConnection();
