import { NextRequest } from 'next/server';
import { authController } from '@/core/controllers';
import { authMiddleware } from '@/app/api/_middleware/auth.middleware';
import logger from '@/utils/logger';
import { errorResponse } from '@/utils/response';
import { ErrorCode } from '@/constants/error-codes';
import { verifyAuth } from '@/utils/auth';

// Tell Next.js this route should always be dynamically rendered
export const dynamic = 'force-dynamic';

/**
 * GET /api/auth/profile - Get user profile
 */
export async function GET(req: NextRequest) {
  try {
    // Check authentication
    const authResponse = authMiddleware(req);
    if (authResponse) return authResponse;

    // Get user from token
    const { user } = verifyAuth(req);
    if (!user) {
      return errorResponse('Authentication required', ErrorCode.UNAUTHORIZED, 401);
    }

    // Get user profile
    return authController.getProfile(user.id);
  } catch (error) {
    logger.error('Get profile error:', error);
    return errorResponse(
      'An unexpected error occurred',
      ErrorCode.INTERNAL_SERVER_ERROR,
      500
    );
  }
}

/**
 * PUT /api/auth/profile - Update user profile
 */
export async function PUT(req: NextRequest) {
  try {
    // Check authentication
    const authResponse = authMiddleware(req);
    if (authResponse) return authResponse;

    // Get user from token
    const { user } = verifyAuth(req);
    if (!user) {
      return errorResponse('Authentication required', ErrorCode.UNAUTHORIZED, 401);
    }

    // Get request body
    const data = await req.json();

    // Update user profile
    return authController.updateProfile(user.id, data);
  } catch (error) {
    logger.error('Update profile error:', error);
    return errorResponse(
      'An unexpected error occurred',
      ErrorCode.INTERNAL_SERVER_ERROR,
      500
    );
  }
}
