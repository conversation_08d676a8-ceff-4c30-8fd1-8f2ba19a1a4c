import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

// This function can be marked `async` if using `await` inside
export function middleware(request: NextRequest) {
  // Get the pathname of the request
  const path = request.nextUrl.pathname;

  // Define public paths that don't require authentication
  const isPublicPath =
    path === '/' || // Allow root path without authentication
    path.startsWith('/auth/') ||
    path.startsWith('/demo/') ||
    path === '/api/auth/login' ||
    path === '/api/auth/register' ||
    path.startsWith('/_next/') || // Allow Next.js static files
    path.startsWith('/favicon') || // Allow favicon
    path.startsWith('/manifest') || // Allow PWA manifest
    path.startsWith('/sw.js') || // Allow service worker
    path.startsWith('/workbox-'); // Allow workbox files

  // Check if the user has a token (basic check)
  const token = request.cookies.get('token')?.value;
  const hasToken = !!token;

  // For protected routes, we'll let the client-side authentication handle the verification
  // The middleware only does basic token presence checks to avoid redirect loops

  // If the path is not public and the user has no token, redirect to landing page
  if (!isPublicPath && !hasToken) {
    console.log(`Redirecting user without token from ${path} to /`);
    return NextResponse.redirect(new URL('/', request.url));
  }

  // If the path is login/register and the user has a token, redirect to home page
  // Note: The client-side will handle invalid tokens and redirect back if needed
  if ((path === '/auth/login' || path === '/auth/register') && hasToken) {
    console.log(`Redirecting user with token from ${path} to /home`);
    return NextResponse.redirect(new URL('/home', request.url));
  }

  // If user has token and trying to access root, redirect to home
  // Note: The client-side will handle invalid tokens and redirect back if needed
  if (path === '/' && hasToken) {
    console.log('Redirecting user with token from / to /home');
    return NextResponse.redirect(new URL('/home', request.url));
  }

  // If user has no token and trying to access home, redirect to root
  if (path === '/home' && !hasToken) {
    console.log('Redirecting user without token from /home to /');
    return NextResponse.redirect(new URL('/', request.url));
  }

  return NextResponse.next();
}

// See "Matching Paths" below to learn more
export const config = {
  matcher: [
    // Match all paths except for:
    // - API routes that don't need authentication checks
    // - Static files (images, etc)
    // - Favicon
    // - Service worker files
    '/((?!api/auth/login|api/auth/register|_next/static|_next/image|favicon.ico|manifest.json|sw.js|workbox-*.js|service-worker.js|offline.html).*)',
  ],
};
