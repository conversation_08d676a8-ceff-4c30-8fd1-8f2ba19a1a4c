import { NextRequest } from 'next/server';
import { timeBlockController } from '@/core/controllers';
import { authMiddleware } from '@/app/api/_middleware/auth.middleware';
import logger from '@/utils/logger';
import { errorResponse } from '@/utils/response';
import { ErrorCode } from '@/constants/error-codes';
import { verifyAuth } from '@/utils/auth';

// Tell Next.js this route should always be dynamically rendered
export const dynamic = 'force-dynamic';

/**
 * GET /api/timeblocks/date/[date] - Get time blocks for a specific date
 */
export async function GET(
  request: NextRequest,
  context: any
) {
  try {
    // Check authentication
    const authResponse = authMiddleware(request);
    if (authResponse) return authResponse;

    // Get user from token
    const { user } = verifyAuth(request);
    if (!user) {
      return errorResponse('Authentication required', ErrorCode.UNAUTHORIZED, 401);
    }

    // Validate date format
    const params = await context.params as { date: string };
    const dateRegex = /^\d{4}-\d{2}-\d{2}$/;
    if (!dateRegex.test(params.date)) {
      return errorResponse('Invalid date format. Use YYYY-MM-DD', ErrorCode.VALIDATION_ERROR, 400);
    }

    // Get time blocks for date
    return timeBlockController.getTimeBlocksByDate(user.id, params.date);
  } catch (error) {
    logger.error('Get time blocks by date error:', error);
    return errorResponse(
      'An unexpected error occurred',
      ErrorCode.INTERNAL_SERVER_ERROR,
      500
    );
  }
}
