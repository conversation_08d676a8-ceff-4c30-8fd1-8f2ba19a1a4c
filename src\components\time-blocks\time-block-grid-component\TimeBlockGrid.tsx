"use client";

import { useState, useEffect, useMemo } from "react";
import { TimeBlock } from "@/lib/types";
import { ScrollArea } from "@/components/ui/scroll-area";
import { usePreferences } from "@/hooks/use-preferences";
import { cn } from "@/lib/utils";
import { format } from "date-fns";
import { TimeBlockDetails } from "./TimeBlockDetails";
import { TimeBlockCard } from "./TimeBlockCard"; // Added import for TimeBlockCard

interface TimeBlockGridProps {
  date: Date;
  timeBlocks: TimeBlock[] | undefined; // Allow undefined for initial loading states
  onAddBlock: (startTime?: string) => void; // Optional: allow passing suggested start time
  onEditBlock: (timeBlock: TimeBlock) => void;
  onDeleteBlock?: (timeBlock: TimeBlock) => void;
  onUpdate: (id: string, data: Partial<TimeBlock>) => void;
}

export function TimeBlockGrid({
  date,
  timeBlocks,
  onAddBlock,
  onEditBlock,
  onDeleteBlock,
  onUpdate,
}: TimeBlockGridProps) {
  const { formatTime, preferences } = usePreferences();
  const [selectedBlock, setSelectedBlock] = useState<TimeBlock | null>(null);
  const [isDetailsOpen, setIsDetailsOpen] = useState(false);
  const [currentSlotIndex, setCurrentSlotIndex] = useState<number>(-1);
  // Memoize time slots calculation for better performance
  const timeSlots = useMemo(() => {
    // Check if using custom time blocks
    if (preferences?.useCustomTimeBlocks && preferences?.customTimeBlocks && preferences.customTimeBlocks.length > 0) {
      // Use custom time blocks
      const customSlots = preferences.customTimeBlocks.map((block, index) => {
        const [startHour, startMinute] = block.startTime.split(':').map(Number);
        const [endHour, endMinute] = block.endTime.split(':').map(Number);

        const startMinutes = startHour * 60 + startMinute;
        let endMinutes = endHour * 60 + endMinute;

        // Handle overnight blocks (e.g., 22:00 to 02:00)
        if (endMinutes <= startMinutes) {
          endMinutes += 24 * 60; // Add 24 hours for next day
        }

        return {
          index,
          startHour,
          startMinute,
          startTime: block.startTime,
          endTime: block.endTime,
          intervalMinutes: endMinutes - startMinutes,
        };
      }).sort((a, b) => {
        // Sort by start time
        const aMinutes = a.startHour * 60 + a.startMinute;
        const bMinutes = b.startHour * 60 + b.startMinute;
        return aMinutes - bMinutes;
      }).map((slot, index) => ({ ...slot, index })); // Re-index after sorting

      return customSlots;
    }

    // Default behavior: use regular intervals
    const intervalMinutes = parseInt(preferences?.timeInterval || "60");
    const totalMinutesInDay = 24 * 60; // 1440 minutes

    // For regular intervals, ensure we cover all 24 hours
    const slots = [];
    let currentMinutes = 0;
    let slotIndex = 0;

    while (currentMinutes < totalMinutesInDay) {
      const startHour = Math.floor(currentMinutes / 60);
      const startMinute = currentMinutes % 60;

      // Calculate end time for this slot
      const endMinutes = Math.min(
        currentMinutes + intervalMinutes,
        totalMinutesInDay
      );
      const endHour = Math.floor(endMinutes / 60);
      const endMinute = endMinutes % 60;

      slots.push({
        index: slotIndex,
        startHour,
        startMinute,
        startTime: `${startHour.toString().padStart(2, "0")}:${startMinute
          .toString()
          .padStart(2, "0")}`,
        endTime: `${endHour.toString().padStart(2, "0")}:${endMinute
          .toString()
          .padStart(2, "0")}`,
        intervalMinutes: endMinutes - currentMinutes,
      });

      currentMinutes = endMinutes;
      slotIndex++;
    }

    return slots;
  }, [preferences?.timeInterval, preferences?.useCustomTimeBlocks, preferences?.customTimeBlocks]);

  // Force re-render when preferences change
  useEffect(() => {
    // This effect ensures the component re-renders when time interval changes
  }, [preferences?.timeInterval]);

  useEffect(() => {
    const updateCurrentSlotMarker = () => {
      const now = new Date();
      const gridDate = new Date(date);

      if (
        now.getFullYear() !== gridDate.getFullYear() ||
        now.getMonth() !== gridDate.getMonth() ||
        now.getDate() !== gridDate.getDate()
      ) {
        setCurrentSlotIndex(-1); // Not today, no marker
        return;
      }

      // Calculate which time slot the current time falls into
      const currentMinutes = now.getHours() * 60 + now.getMinutes();

      if (preferences?.useCustomTimeBlocks && preferences?.customTimeBlocks && preferences.customTimeBlocks.length > 0) {
        // Find which custom time block the current time falls into
        const currentSlot = timeSlots.findIndex(slot => {
          const slotStartMinutes = slot.startHour * 60 + slot.startMinute;
          let slotEndMinutes = slot.startHour * 60 + slot.startMinute + slot.intervalMinutes;
          
          // Handle overnight slots
          if (slotEndMinutes <= slotStartMinutes) {
            slotEndMinutes += 24 * 60;
          }
          
          // For current time after midnight but before slot end time (overnight slot)
          if (currentMinutes < slotStartMinutes && slotEndMinutes > 24 * 60) {
            return currentMinutes < (slotEndMinutes - 24 * 60);
          }
          
          return currentMinutes >= slotStartMinutes && currentMinutes < slotEndMinutes;
        });
        setCurrentSlotIndex(currentSlot);
      } else {
        // Regular interval calculation
        const intervalMinutes = parseInt(preferences?.timeInterval || "60");
        const currentSlot = Math.floor(currentMinutes / intervalMinutes);
        setCurrentSlotIndex(currentSlot);
      }
    };

    updateCurrentSlotMarker();
    const timer = setInterval(updateCurrentSlotMarker, 60000); // Update every minute
    return () => clearInterval(timer);
  }, [date, preferences?.timeInterval, preferences?.useCustomTimeBlocks, preferences?.customTimeBlocks, timeSlots]);

  const getBlocksForSlot = (slot: {
    index: number;
    startHour: number;
    startMinute: number;
    startTime: string;
    endTime: string;
    intervalMinutes: number;
  }): TimeBlock[] => {
    if (!timeBlocks) return [];

    // Create a Map to track unique blocks by a composite key to prevent duplicates
    const uniqueBlocks = new Map<string, TimeBlock>();

    // Format the selected date for comparison
    const selectedDateStr = format(date, "yyyy-MM-dd");

    // Calculate slot time range in minutes
    const slotStartMinutes = slot.startHour * 60 + slot.startMinute;
    let slotEndMinutes: number;

    // For custom time blocks, use the exact end time
    if (preferences?.useCustomTimeBlocks && slot.endTime) {
      const [endHour, endMinute] = slot.endTime.split(":").map(Number);
      slotEndMinutes = endHour * 60 + endMinute;
      // Handle overnight slots (end time is next day)
      if (slotEndMinutes <= slotStartMinutes) {
        slotEndMinutes += 24 * 60;
      }
    } else {
      // For regular intervals, calculate end time based on interval
      slotEndMinutes = slotStartMinutes + slot.intervalMinutes;
    }

    // Filter blocks that belong to this time slot and have time information
    timeBlocks.forEach((block) => {
      // Skip blocks without time information
      if (!block.startTime || !block.endTime) return;

      // Check if the block is for the selected date
      const blockDateStr = block.date;
      const isForSelectedDate = blockDateStr === selectedDateStr;

      // Only show blocks for the selected date
      if (!isForSelectedDate && !block.routineId) {
        return;
      }

      // For routine tasks, ensure we only show the ones for the selected date
      if (block.routineId) {
        const blockDate = new Date(block.date);
        const selectedDate = new Date(date);

        // Only show routine tasks that match the selected date
        if (
          blockDate.getDate() !== selectedDate.getDate() ||
          blockDate.getMonth() !== selectedDate.getMonth() ||
          blockDate.getFullYear() !== selectedDate.getFullYear()
        ) {
          return;
        }
      }

      const [startHour, startMinute] = block.startTime.split(":").map(Number);
      const [endHour, endMinute] = block.endTime.split(":").map(Number);

      const blockStartTotalMinutes = startHour * 60 + startMinute;
      let blockEndTotalMinutes = endHour * 60 + endMinute;

      // Handle overnight blocks
      if (blockEndTotalMinutes <= blockStartTotalMinutes) {
        blockEndTotalMinutes += 24 * 60;
      }

      // Calculate slot end time properly for custom time blocks
      let slotEndMinutes: number;
      if (preferences?.useCustomTimeBlocks && slot.endTime) {
        const [endHour, endMinute] = slot.endTime.split(":").map(Number);
        slotEndMinutes = endHour * 60 + endMinute;
        if (slotEndMinutes <= slotStartMinutes) {
          slotEndMinutes += 24 * 60;
        }
      } else {
        slotEndMinutes = slotStartMinutes + slot.intervalMinutes;
      }

      // Check if the block intersects with the current time slot
      const isInSlot =
        (blockStartTotalMinutes < slotEndMinutes && blockEndTotalMinutes > slotStartMinutes) ||
        // Handle overnight blocks that wrap around midnight
        (blockEndTotalMinutes > 24 * 60 && 
         ((blockStartTotalMinutes < slotEndMinutes) || 
          (blockEndTotalMinutes - 24 * 60 > slotStartMinutes)));

      if (isInSlot) {
        // Create a unique key for this block that includes all relevant properties
        // For routine tasks, include the routineId to ensure we don't show duplicates
        const uniqueKey = block.routineId
          ? `${block.routineId}-${block.startTime}-${block.endTime}-${block.date}`
          : `${block.id}-${block.startTime}-${block.endTime}`;

        // Only add this block if we haven't seen it before with the same key
        if (!uniqueBlocks.has(uniqueKey)) {
          uniqueBlocks.set(uniqueKey, block);
          return;
        }
      }
    });

    // Return the unique blocks for this time slot
    return Array.from(uniqueBlocks.values());
  };

  const handleAddBlock = (slot: {
    startHour: number;
    startMinute: number;
    startTime: string;
  }) => {
    onAddBlock(slot.startTime);
  };

  return (
    <div className="flex flex-col h-full">
      <ScrollArea className="flex-grow h-[calc(100vh_-_100px)]">
        {" "}
        {/* Adjust 100px based on actual header/nav height */}
        <div className="p-1 pr-2">
          {" "}
          {/* Added pr-2 for scrollbar */}
          {timeSlots.map((slot, index) => {
            const blocksForThisSlot = getBlocksForSlot(slot);

            // Create display label for the time slot
            const getSlotLabel = () => {
              return {
                startTime: formatTime(slot.startTime),
                endTime: formatTime(slot.endTime),
                isRange: true,
              };
            };

            const slotLabel = getSlotLabel();

            return (
              <div
                key={slot.index}
                className={cn(
                  "grid grid-cols-[60px_1fr] sm:grid-cols-[65px_1fr] md:grid-cols-[80px_1fr] items-stretch",
                  index < timeSlots.length - 1 && "border-b" // Add border to all but the last item
                )}
              >
                {/* Time Slot Label Cell */}
                <div
                  className={cn(
                    "sticky left-0 bg-background z-10 py-1 px-0.5 flex items-center justify-end border-r select-none",
                    index === currentSlotIndex
                      ? "text-primary font-semibold"
                      : "text-muted-foreground"
                  )}
                >
                  <div className="text-right leading-none">
                    {slotLabel.isRange ? (
                      <div className="flex flex-col items-end space-y-0">
                        <div className="text-xs font-medium">
                          {slotLabel.startTime}
                        </div>
                        <div className=" text-xs text-muted-foreground leading-none">
                          —
                        </div>
                        <div className="text-xs font-medium">
                          {slotLabel.endTime}
                        </div>
                      </div>
                    ) : (
                      <div className="text-xs font-medium">
                        {slotLabel.startTime}
                      </div>
                    )}
                  </div>
                </div>

                {/* Cards Container Cell */}
                <div
                  className={cn(
                    "flex overflow-x-auto space-x-2 p-2 min-h-[136px] items-center scrollbar-thin scrollbar-thumb-rounded scrollbar-track-transparent",
                    index === currentSlotIndex ? "bg-primary/5" : "",
                    "hover:scrollbar-thumb-muted-foreground/40 scrollbar-thumb-muted-foreground/20"
                  )}
                >
                  {blocksForThisSlot.length === 0 ? (
                    <div
                      className="flex-1 h-full min-h-[120px] flex items-center justify-center text-xs text-muted-foreground rounded cursor-pointer hover:bg-muted/30 transition-colors"
                      onClick={() => handleAddBlock(slot)}
                    >
                      Click to add note.
                  </div>
                  ) : (
                    blocksForThisSlot.map((block) => {
                      // Create a more comprehensive unique key to avoid duplicate blocks
                      const blockKey = block.routineId
                        ? `${block.routineId}-${block.id}-${block.startTime}-${block.endTime}-${block.date}`
                        : block.id;

                      return (
                        <div
                          key={blockKey}
                          className="h-[128px] flex-shrink-0 transition-all hover:scale-[1.02]"
                        >
                          <TimeBlockCard
                            block={block}
                            onEdit={onEditBlock}
                            onUpdate={onUpdate}
                            onClick={() => {
                              setSelectedBlock(block);
                              setIsDetailsOpen(true);
                            }}
                          />
                        </div>
                      );
                    })
                  )}
                </div>
              </div>
            );
          })}
        </div>
      </ScrollArea>

      <TimeBlockDetails
        isOpen={isDetailsOpen}
        onClose={() => setIsDetailsOpen(false)}
        timeBlock={selectedBlock}
        date={date}
        onEdit={onEditBlock}
        onDelete={onDeleteBlock}
      />
    </div>
  );
}
