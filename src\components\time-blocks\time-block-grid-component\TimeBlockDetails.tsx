"use client"

import { format } from 'date-fns';
import { Clock, Calendar, Trash, Pencil } from 'lucide-react';
import { <PERSON><PERSON>, DialogHeader, DialogTitle, DialogFooter } from '@/components/ui/dialog';
import { CustomDialogContent } from '@/components/ui/custom-dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { TimeBlock } from '@/lib/types';
import { useCategories } from '@/hooks/use-categories';
import { usePreferences } from '@/hooks/use-preferences';
import { cn } from '@/lib/utils';
import { toast } from 'sonner';

interface TimeBlockDetailsProps {
  isOpen: boolean;
  onClose: () => void;
  timeBlock: TimeBlock | null;
  date: Date;
  onEdit: (timeBlock: TimeBlock) => void;
  onDelete?: (timeBlock: TimeBlock) => void;
}

export function TimeBlockDetails({
  isOpen,
  onClose,
  timeBlock,
  date,
  onEdit,
  onDelete
}: TimeBlockDetailsProps) {
  const { categories } = useCategories();
  const { formatTime } = usePreferences();

  // Get color based on category
  const getCategoryColor = (block: TimeBlock) => {
    if (block.categoryData?.color) {
      return block.categoryData.color;
    }

    const categoryId = typeof block.category === 'object' && block.category !== null
      ? (block.category as any).id || block.category
      : block.category;

    const category = categories.find((c) => c.id === categoryId);
    return category?.color || "#6b7280";
  };

  // Get text color based on background color
  const getTextColor = (bgColor: string) => {
    try {
      const hex = bgColor.replace('#', '');
      const r = parseInt(hex.substr(0, 2), 16);
      const g = parseInt(hex.substr(2, 2), 16);
      const b = parseInt(hex.substr(4, 2), 16);

      const luminance = (0.299 * r + 0.587 * g + 0.114 * b) / 255;
      return luminance > 0.5 ? 'text-gray-900' : 'text-white';
    } catch (e) {
      return 'text-white';
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen => !setIsOpen && onClose()}>
      <CustomDialogContent className="max-w-md p-4 max-h-screen overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center">
            <Clock className="h-4 w-4 mr-2" />
            Time Block Details
          </DialogTitle>
          {timeBlock && (
            <div className="mt-1">
              <Badge variant="outline">
                {formatTime(timeBlock.startTime)} - {formatTime(timeBlock.endTime)}
              </Badge>
            </div>
          )}
        </DialogHeader>

        {timeBlock && (
          <div className="space-y-4 py-2">
            <div>
              <h4 className="text-sm font-medium mb-1">Category</h4>
              <div className="flex items-center gap-2">
                <div
                  className="w-4 h-4 rounded-full border border-border/50 flex-shrink-0"
                  style={{ backgroundColor: getCategoryColor(timeBlock) }}
                />
                <Badge
                  style={{
                    backgroundColor: getCategoryColor(timeBlock),
                  }}
                  className={cn(
                    "border-0 px-3 py-1 text-sm font-medium",
                    getTextColor(getCategoryColor(timeBlock))
                  )}
                >
                  {timeBlock.categoryData?.name ||
                    categories.find(c => c.id === timeBlock.category)?.name ||
                    'Unknown Category'}
                </Badge>
              </div>
            </div>

            <div>
              <h4 className="text-sm font-medium mb-1">Note</h4>
              <div className="mt-2 h-40 border rounded-md p-3 bg-muted/40 overflow-y-auto overflow-x-hidden break-words text-sm">
                {timeBlock.note}
              </div>
            </div>

            <div>
              <h4 className="text-sm font-medium mb-1">Date</h4>
              <div className="flex items-center text-sm">
                <Calendar className="h-4 w-4 mr-2 text-muted-foreground" />
                {format(date, "EEEE, MMMM d, yyyy")}
              </div>
            </div>
          </div>
        )}

        <DialogFooter className="flex flex-row items-center justify-between gap-2 mt-4 flex-wrap sm:flex-nowrap">
          <Button
            variant="outline"
            size="sm"
            onClick={() => {
              if (timeBlock && timeBlock.id) {
                onEdit(timeBlock);
                onClose();
              } else {
                toast.error("Cannot edit time block: Invalid ID");
              }
            }}
            className="flex-1 h-9"
          >
            <Pencil className="h-4 w-4 mr-2" />
            Edit
          </Button>
          {onDelete && timeBlock && (
            <Button
              variant="outline"
              size="sm"
              className="text-destructive hover:bg-destructive/10 flex-1 h-9"
              onClick={() => {
                if (timeBlock && timeBlock.id) {
                  onDelete(timeBlock);
                  onClose();
                } else {
                  toast.error("Cannot delete time block: Invalid ID");
                }
              }}
            >
              <Trash className="h-4 w-4 mr-2" />
              Delete
            </Button>
          )}
          <Button variant="secondary" size="sm" onClick={onClose} className="flex-1 h-9">
            Close
          </Button>
        </DialogFooter>
      </CustomDialogContent>
    </Dialog>
  );
}
