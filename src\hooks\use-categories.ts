import { useState, useEffect, useCallback } from 'react';
import { toast } from 'sonner';
import { Category } from '@/lib/types';

// Use the Category type from lib/types.ts which is re-exported from src/types/category.ts
// This avoids duplicate type definitions

// Cache the categories data in memory
let cachedCategories = {
  data: [] as Category[],
  lastFetched: 0
};

const CATEGORIES_CACHE_TIME = 5 * 60 * 1000; // 5 minutes

export function useCategories() {
  const [categories, setCategories] = useState<Category[]>(cachedCategories.data);
  const [loading, setLoading] = useState(cachedCategories.data.length === 0);

  // Helper function to try loading categories from localStorage
  const tryLoadFromLocalStorage = useCallback(() => {
    try {
      const storedCategories = localStorage.getItem('categories');

      if (storedCategories) {
        const parsedCategories = JSON.parse(storedCategories);
        if (Array.isArray(parsedCategories)) {
          setCategories(parsedCategories);

          // Update cache
          cachedCategories = {
            data: parsedCategories,
            lastFetched: Date.now() - (CATEGORIES_CACHE_TIME / 2) // Set to half expired so it refreshes soon but not immediately
          };

          return true;
        }
      }
    } catch (e) {
      // Silent fail - will return false below
    }
    return false;
  }, []);

  // Fetch categories
  const fetchCategories = useCallback(async (forceRefresh = false) => {
    try {
      const now = Date.now();

      // Return cached value if valid and not forcing refresh
      if (!forceRefresh && cachedCategories.data.length > 0 && now - cachedCategories.lastFetched < CATEGORIES_CACHE_TIME) {
        setCategories(cachedCategories.data);
        setLoading(false);
        return;
      }

      // Try to load from localStorage first if available and cache is expired
      if (!forceRefresh && tryLoadFromLocalStorage()) {
        setLoading(false);
        return;
      }

      setLoading(true);

      const response = await fetch('/api/categories');
      if (!response.ok) throw new Error('Failed to fetch categories');
      const responseData = await response.json();

      // Check if the response has the expected structure
      if (responseData && responseData.success === true && responseData.data) {
        // Ensure data is an array before setting it
        if (Array.isArray(responseData.data)) {
          // Update cache
          cachedCategories = {
            data: responseData.data,
            lastFetched: now
          };

          setCategories(responseData.data);

          // Store in localStorage for offline access
          localStorage.setItem('categories', JSON.stringify(responseData.data));
        } else {
          setCategories([]);
          toast.error('Invalid categories data received');
        }
      } else {
        setCategories([]);
        toast.error('Invalid response format from server');
      }
    } catch (error) {
      toast.error('Failed to load categories');
      // Try to load from localStorage as fallback
      tryLoadFromLocalStorage();
    } finally {
      setLoading(false);
    }
  }, [tryLoadFromLocalStorage]);

  // Create new category
  const createCategory = async (name: string, color: string) => {
    try {
      const response = await fetch('/api/categories', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ name, color }),
      });

      if (!response.ok) throw new Error('Failed to create category');

      const responseData = await response.json();

      // Check if the response has the expected structure
      if (responseData && responseData.success === true && responseData.data) {
        const newCategory = responseData.data;
        
        // Force a refresh of the categories list
        await fetchCategories(true);
        
        // Return the new category
        toast.success('Category created successfully');
        return newCategory;
      } else {
        toast.error('Invalid response format from server');
        throw new Error('Invalid response format from server');
      }
    } catch (error) {
      toast.error('Failed to create category');
      throw error;
    }
  };

  // Get category by name
  const getCategoryByName = (name: string) => {
    return categories.find(cat => cat.name === name);
  };

  // Delete category
  const deleteCategory = async (id: string) => {
    try {
      const response = await fetch(`/api/categories/${id}`, {
        method: 'DELETE',
        credentials: 'include',
      });

      if (!response.ok) throw new Error('Failed to delete category');

      const responseData = await response.json();

      // Check if the response has the expected structure
      if (responseData && responseData.success === true) {
        // Remove the category from state
        const updatedCategories = categories.filter(cat => cat.id !== id);

        // Update state
        setCategories(updatedCategories);

        // Update cache
        cachedCategories = {
          data: updatedCategories,
          lastFetched: Date.now()
        };

        // Update localStorage
        localStorage.setItem('categories', JSON.stringify(updatedCategories));

        toast.success('Category deleted successfully');
        return true;
      } else {
        toast.error('Invalid response format from server');
        return false;
      }
    } catch (error) {
      toast.error('Failed to delete category');
      return false;
    }
  };

  // Load categories on mount
  useEffect(() => {
    fetchCategories();
  }, [fetchCategories]);

  return {
    categories,
    loading,
    createCategory,
    deleteCategory,
    getCategoryByName,
    refresh: (forceRefresh = false) => fetchCategories(forceRefresh),
  };
}
