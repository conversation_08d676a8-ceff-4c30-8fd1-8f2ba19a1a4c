import { TimeBlock } from "@/lib/types";
import { Category } from "@/lib/types"; // Assuming you have Category type

export function getCategoryColor(block: TimeBlock, categories: Category[]) {
  if (block.categoryData?.color) {
    return block.categoryData.color;
  }

  const categoryId = typeof block.category === 'object' && block.category !== null
    ? (block.category as any).id || block.category
    : block.category;

  const category = categories.find((c) => c.id === categoryId);
  return category?.color || "#6b7280";
}

export function getTextColor(bgColor: string) {
  try {
    const hex = bgColor.replace('#', '');
    const r = parseInt(hex.substr(0, 2), 16);
    const g = parseInt(hex.substr(2, 2), 16);
    const b = parseInt(hex.substr(4, 2), 16);

    const luminance = (0.299 * r + 0.587 * g + 0.114 * b) / 255;
    return luminance > 0.5 ? 'text-gray-900' : 'text-white';
  } catch (e) {
    return 'text-white';
  }
}
