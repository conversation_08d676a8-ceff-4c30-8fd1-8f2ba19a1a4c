/**
 * Debug database connections and indexes
 * This script checks all databases and collections for email indexes
 * Run with: node src/scripts/debug-database.js
 */

const mongoose = require('mongoose');
const path = require('path');
const fs = require('fs');

// Load environment variables from .env file
const envPath = path.join(__dirname, '../../.env');
if (fs.existsSync(envPath)) {
  const envContent = fs.readFileSync(envPath, 'utf8');
  const envLines = envContent.split('\n');
  envLines.forEach(line => {
    const equalIndex = line.indexOf('=');
    if (equalIndex > 0) {
      const key = line.substring(0, equalIndex).trim();
      const value = line.substring(equalIndex + 1).trim();
      if (key && value) {
        process.env[key] = value;
      }
    }
  });
}

const MONGODB_URI = process.env.MONGODB_URI;

async function debugDatabase() {
  try {
    console.log('🔍 Debugging database connections and indexes...');
    console.log('MongoDB URI:', MONGODB_URI.replace(/\/\/[^:]+:[^@]+@/, '//***:***@'));
    
    console.log('\nConnecting to MongoDB...');
    await mongoose.connect(MONGODB_URI);
    console.log('Connected to MongoDB successfully');
    
    const client = mongoose.connection.getClient();
    const admin = client.db().admin();
    
    // List all databases
    console.log('\n📋 Listing all databases...');
    const databases = await admin.listDatabases();
    console.log('Found databases:');
    databases.databases.forEach((db, i) => {
      console.log(`${i + 1}. ${db.name} (${(db.sizeOnDisk / 1024 / 1024).toFixed(2)} MB)`);
    });
    
    // Check current database
    const currentDb = mongoose.connection.db;
    console.log(`\n🎯 Current database: ${currentDb.databaseName}`);
    
    // List collections in current database
    console.log('\n📁 Collections in current database:');
    const collections = await currentDb.listCollections().toArray();
    collections.forEach((collection, i) => {
      console.log(`${i + 1}. ${collection.name}`);
    });
    
    // Check users collection specifically
    if (collections.some(c => c.name === 'users')) {
      console.log('\n👥 Checking users collection...');
      const usersCollection = currentDb.collection('users');
      
      // List all indexes
      console.log('\nIndexes on users collection:');
      const indexes = await usersCollection.indexes();
      indexes.forEach((index, i) => {
        console.log(`${i + 1}. ${index.name}: ${JSON.stringify(index.key)}`);
        if (index.key && index.key.email !== undefined) {
          console.log(`   ⚠️  EMAIL INDEX FOUND: ${index.name}`);
        }
      });
      
      // Count users
      const userCount = await usersCollection.countDocuments();
      console.log(`\nTotal users: ${userCount}`);
      
      // Check for users with email fields
      const usersWithEmail = await usersCollection.countDocuments({ email: { $exists: true } });
      console.log(`Users with email field: ${usersWithEmail}`);
      
      if (usersWithEmail > 0) {
        console.log('\nSample users with email fields:');
        const sampleUsers = await usersCollection.find({ email: { $exists: true } }).limit(3).toArray();
        sampleUsers.forEach((user, i) => {
          console.log(`${i + 1}. ID: ${user._id}, Email: ${user.email}, AccessKey: ${user.accessKey || 'none'}`);
        });
      }
    } else {
      console.log('\n❌ No users collection found in current database');
    }
    
    // Check all databases for users collections with email indexes
    console.log('\n🔍 Checking all databases for email indexes...');
    for (const dbInfo of databases.databases) {
      if (dbInfo.name === 'admin' || dbInfo.name === 'local' || dbInfo.name === 'config') {
        continue; // Skip system databases
      }
      
      console.log(`\nChecking database: ${dbInfo.name}`);
      const db = client.db(dbInfo.name);
      
      try {
        const collections = await db.listCollections().toArray();
        const usersCollection = collections.find(c => c.name === 'users');
        
        if (usersCollection) {
          console.log(`  Found users collection in ${dbInfo.name}`);
          const collection = db.collection('users');
          const indexes = await collection.indexes();
          
          const emailIndexes = indexes.filter(index => index.key && index.key.email !== undefined);
          if (emailIndexes.length > 0) {
            console.log(`  ⚠️  EMAIL INDEXES FOUND in ${dbInfo.name}:`);
            emailIndexes.forEach(index => {
              console.log(`    - ${index.name}: ${JSON.stringify(index.key)}`);
            });
          } else {
            console.log(`  ✅ No email indexes in ${dbInfo.name}`);
          }
        }
      } catch (error) {
        console.log(`  ❌ Error checking ${dbInfo.name}:`, error.message);
      }
    }
    
    console.log('\n✅ Database debug completed!');
    
  } catch (error) {
    console.error('❌ Error:', error);
  } finally {
    // Close the connection
    if (mongoose.connection.readyState !== 0) {
      await mongoose.disconnect();
      console.log('\nDisconnected from MongoDB');
    }
  }
}

// Run the function
debugDatabase();
