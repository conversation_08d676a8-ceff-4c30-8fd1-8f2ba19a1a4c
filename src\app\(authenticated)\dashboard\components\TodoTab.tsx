"use client"

import { format, parseISO, isToday, isTomorrow } from 'date-fns';
import { Clock, Plus, CheckCircle2, Circle } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Card } from '@/components/ui/card';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Badge } from '@/components/ui/badge';
import { TimeBlock } from '@/lib/types';
import { cn } from '@/lib/utils';
import { useCategories } from '@/hooks/use-categories';
import { usePreferences } from '@/hooks/use-preferences';
import { getCategoryColor } from '@/components/time-blocks/utils';

interface TodoTabProps {
  selectedDate: Date;
  filteredBlocks: TimeBlock[];
  loading: boolean;
  onAddBlock: () => void;
  onEditBlock: (block: TimeBlock) => void;
  onDeleteBlock: (id: string) => void;
  onUpdateBlock: (id: string, data: Partial<TimeBlock>) => void;
}

// Helper function to group todos by exact date
const groupTodosByExactDate = (todos: TimeBlock[]) => {
  // Create an object to hold todos grouped by date string
  const dateGroups: { [dateStr: string]: { label: string, date: Date, todos: TimeBlock[] } } = {};

  // Sort todos by date (ascending)
  const sortedTodos = [...todos].sort((a, b) => {
    const dateA = parseISO(a.date);
    const dateB = parseISO(b.date);
    return dateA.getTime() - dateB.getTime();
  });

  // Group todos by their exact date
  sortedTodos.forEach(todo => {
    const todoDate = parseISO(todo.date);
    const dateStr = format(todoDate, 'yyyy-MM-dd');

    if (!dateGroups[dateStr]) {
      // Create a formatted label for the date
      let label = format(todoDate, 'EEEE, MMMM d');

      // Add special label for today/tomorrow
      if (isToday(todoDate)) {
        label = `Today - ${label}`;
      } else if (isTomorrow(todoDate)) {
        label = `Tomorrow - ${label}`;
      }

      dateGroups[dateStr] = {
        label,
        date: todoDate,
        todos: []
      };
    }

    dateGroups[dateStr].todos.push(todo);
  });

  return dateGroups;
};

// Slim Todo Item Component
const SlimTodoItem = ({
  todo,
  onToggleComplete,
  onEdit
}: {
  todo: TimeBlock,
  onToggleComplete: () => void,
  onEdit: () => void
}) => {
  const { categories } = useCategories();
  const { formatTime } = usePreferences();

  // Get enhanced category color with fallbacks
  const getEnhancedCategoryColor = (block: TimeBlock) => {
    // First try the utility function
    const baseColor = getCategoryColor(block, categories);

    // If we got a valid color from the utility, return it
    if (baseColor !== "#6b7280") {
      return baseColor;
    }

    // Additional fallbacks for better UX
    if (block.categoryData?.name) {
      const name = block.categoryData.name.toLowerCase();
      if (name.includes('work')) return '#4f46e5';
      if (name.includes('personal')) return '#10b981';
      if (name.includes('study')) return '#f59e0b';
      if (name.includes('health')) return '#ef4444';
      if (name.includes('leisure')) return '#8b5cf6';
    }

    // Final fallback
    return baseColor;
  };

  const categoryColor = getEnhancedCategoryColor(todo);

  // Get category name
  let categoryName = 'Category';
  if (todo.categoryData?.name) {
    categoryName = todo.categoryData.name;
  } else {
    // Try to find category by ID
    let categoryId = '';
    if (typeof todo.category === 'string') {
      categoryId = todo.category;
    } else if (typeof todo.category === 'object' && todo.category !== null) {
      categoryId = (todo.category as any).id || (todo.category as any)._id || '';
    }

    if (categoryId) {
      const category = categories.find(c => c.id === categoryId);
      if (category?.name) {
        categoryName = category.name;
      }
    }
  }

  const todoDate = parseISO(todo.date);

  return (
    <div
      className={cn(
        "flex items-center p-2 border-l-2 hover:bg-muted/30 rounded-sm group border border-border/60",
        todo.isCompleted ? "border-green-500 bg-green-50 dark:bg-green-900/10" : ""
      )}
      style={{
        borderLeftColor: todo.isCompleted ? 'rgb(34, 197, 94)' : categoryColor
      }}
    >
      <Button
        variant="ghost"
        size="icon"
        className={cn(
          "h-8 w-8 rounded-full mr-2 flex-shrink-0",
          todo.isCompleted
            ? "text-green-600 hover:bg-green-100 border-2 border-green-500"
            : "text-muted-foreground hover:text-primary hover:bg-primary/5 border-2 border-muted-foreground/50"
        )}
        onClick={onToggleComplete}
      >
        {todo.isCompleted ? (
          <CheckCircle2 className="h-5 w-5" />
        ) : (
          <Circle className="h-5 w-5" />
        )}
      </Button>

      <div className="flex-grow min-w-0 cursor-pointer" onClick={onEdit}>
        <div className="flex items-center gap-2 mb-1">
          <p className={cn(
            "text-sm font-medium truncate",
            todo.isCompleted && "text-green-800 dark:text-green-300 line-through"
          )}>
            {todo.note}
          </p>
        </div>

        <div className="flex items-center text-xs text-muted-foreground">
          <Badge
            variant="outline"
            className="h-5 px-2 text-xs font-medium rounded-full mr-2"
            style={{
              backgroundColor: `${categoryColor}20`,
              color: categoryColor,
              borderColor: `${categoryColor}60`,
            }}
          >
            {categoryName}
          </Badge>

          <span className="text-xs">
            {format(todoDate, "MMM d")} • {formatTime(todo.startTime)} - {formatTime(todo.endTime)}
          </span>
        </div>
      </div>
    </div>
  );
};

export function TodoTab({
  selectedDate,
  filteredBlocks,
  loading,
  onAddBlock,
  onEditBlock,
  onDeleteBlock,
  onUpdateBlock
}: TodoTabProps) {
  // Group todos by date
  const groupedTodos = groupTodosByExactDate(filteredBlocks);

  return (
    <Card className="shadow-sm border-0 overflow-hidden">
      {loading ? (
        <div className="flex justify-center p-4">
          <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
        </div>
      ) : filteredBlocks.length > 0 ? (
        <ScrollArea className="h-[calc(100vh-200px)] md:h-[calc(100vh-100px)]">
          <div className="p-2">
            {Object.entries(groupedTodos).map(([dateStr, group]) =>
              group.todos.length > 0 && (
                <div key={dateStr} className="mb-4">
                  <div className="flex items-center mb-2 px-2">
                    <h3 className="text-sm font-medium text-muted-foreground">{group.label}</h3>
                    <div className="ml-2 text-xs text-muted-foreground/60">
                      ({group.todos.length} {group.todos.length === 1 ? 'todo' : 'todos'})
                    </div>
                  </div>
                  <div className="space-y-1">
                    {group.todos.map((todo) => (
                      <SlimTodoItem
                        key={todo.id}
                        todo={todo}
                        onToggleComplete={() => {
                          console.log('Toggling todo completion:', todo.id, !todo.isCompleted);
                          // Add a try-catch block to handle errors
                          try {
                            onUpdateBlock(todo.id, { isCompleted: !todo.isCompleted });
                          } catch (error) {
                            console.error("Exception updating todo:", error);
                          }
                        }}
                        onEdit={() => onEditBlock(todo)}
                      />
                    ))}
                  </div>
                </div>
              )
            )}
          </div>
        </ScrollArea>
      ) : (
        <div className="flex flex-col items-center justify-center p-8 text-center">
          <div className="rounded-full bg-primary/10 p-3 mb-3">
            <Clock className="h-6 w-6 text-primary" />
          </div>
          <p className="text-base font-medium mb-2">No todos yet</p>
          <p className="text-muted-foreground mb-4 max-w-md text-sm">
            Add a todo item to start tracking your tasks.
          </p>
          <Button
            onClick={onAddBlock}
            size="sm"
            variant="outline"
            className="border border-primary/30 hover:bg-primary/10 text-primary rounded-full px-3 h-8"
          >
            <Plus className="h-3 w-3 mr-1" />
            Add Todo
          </Button>
        </div>
      )}
    </Card>
  );
}
