"use client"

import { Card, CardContent } from "@/components/ui/card";
import { cn } from "@/lib/utils";
import { usePreferences } from "@/hooks/use-preferences";
import { format } from "date-fns";

const WEEKDAYS = [
  { value: 0, label: "Sunday", date: new Date(new Date().setDate(new Date().getDate() - new Date().getDay())) },
  { value: 1, label: "Monday", date: new Date(new Date().setDate(new Date().getDate() - new Date().getDay() + 1)) },
  { value: 2, label: "Tuesday", date: new Date(new Date().setDate(new Date().getDate() - new Date().getDay() + 2)) },
  { value: 3, label: "Wednesday", date: new Date(new Date().setDate(new Date().getDate() - new Date().getDay() + 3)) },
  { value: 4, label: "Thursday", date: new Date(new Date().setDate(new Date().getDate() - new Date().getDay() + 4)) },
  { value: 5, label: "Friday", date: new Date(new Date().setDate(new Date().getDate() - new Date().getDay() + 5)) },
  { value: 6, label: "Saturday", date: new Date(new Date().setDate(new Date().getDate() - new Date().getDay() + 6)) },
];

interface Routine {
  id: string;
  title: string;
  startTime: string;
  endTime: string;
  days: number[];
  note?: string;
  categoryId: string;
  categoryData?: {
    name: string;
    color: string;
  };
}

interface RoutineWeekViewProps {
  routines: Routine[];
  onEditRoutine: (routine: Routine) => void;
  onDeleteRoutine: (id: string) => void;
}

export function RoutineWeekView({ routines, onEditRoutine, onDeleteRoutine }: RoutineWeekViewProps) {
  // Get user preferences for time formatting
  const { formatTime } = usePreferences();

  // No longer needed with the new layout approach

  // Get text color based on background color for better contrast
  const getTextColor = (bgColor: string) => {
    // Convert hex to RGB
    const hex = bgColor.replace('#', '');
    const r = parseInt(hex.substring(0, 2), 16);
    const g = parseInt(hex.substring(2, 4), 16);
    const b = parseInt(hex.substring(4, 6), 16);

    // Calculate luminance
    const luminance = (0.299 * r + 0.587 * g + 0.114 * b) / 255;

    // Return white for dark backgrounds, black for light backgrounds
    return luminance > 0.5 ? '#000000' : '#ffffff';
  };

  const today = new Date().getDay();

  // Generate week view grid
  const renderWeekView = () => {
    // Show all 24 hours for better visibility
    const hours = Array.from({ length: 24 }, (_, i) => i);

    return (
      <div className="relative">
        {/* Fixed header with days */}
        <div className="grid grid-cols-[60px_repeat(7,minmax(150px,1fr))] border-b sticky top-0 bg-background z-20">
          <div className="sticky left-0 bg-background z-30 border-r"></div>
          {WEEKDAYS.map((day) => (
            <div
              key={day.value}
              className={cn(
                "p-2 text-center border-r",
                day.value === today && "bg-primary/5"
              )}
            >
              <div className="text-xs font-medium">{day.label}</div>
              <div
                className={cn(
                  "text-sm sm:text-base font-medium",
                  day.value === today && "text-primary"
                )}
              >
                {format(day.date, 'd')}
              </div>
            </div>
          ))}
        </div>

        {/* Time grid */}
        <div className="relative">
          {hours.map((hour) => (
            <div key={hour} className="grid grid-cols-[60px_repeat(7,minmax(150px,1fr))] border-b">
              {/* Hour label - fixed on scroll */}
              <div className="sticky left-0 bg-background z-10 p-2 text-xs text-muted-foreground flex items-center justify-end pr-2 border-r">
                {formatTime(`${hour.toString().padStart(2, '0')}:00`)}
              </div>

              {/* Day columns */}
              {WEEKDAYS.map((day) => {
                const routinesForHour = routines.filter(routine => {
                  if (!routine.days.includes(day.value)) return false;

                  const startHour = parseInt(routine.startTime.split(':')[0], 10);
                  const endHour = parseInt(routine.endTime.split(':')[0], 10);
                  const endMinutes = parseInt(routine.endTime.split(':')[1], 10);

                  // Include routines that overlap with this hour
                  return (startHour <= hour && (endHour > hour || (endHour === hour && endMinutes > 0))) ||
                         (startHour === hour);
                });

                return (
                  <div
                    key={day.value}
                    className={cn(
                      "p-1 border-r min-h-[60px] relative",
                      day.value === today && "bg-primary/5"
                    )}
                  >
                    {routinesForHour.map((routine) => {
                      const startHour = parseInt(routine.startTime.split(':')[0], 10);

                      // Only show the routine if it starts in this hour or is the first hour it appears in
                      const shouldRender = startHour === hour ||
                        (hour === 0 && startHour === 0) ||
                        (startHour < hour && hour === Math.floor(startHour));

                      if (!shouldRender) return null;

                      return (
                        <div
                          key={`${routine.id}-${day.value}`}
                          className="text-xs p-1.5 rounded mb-1 shadow-sm cursor-pointer overflow-hidden hover:shadow-md transition-shadow relative"
                          style={{
                            backgroundColor: routine.categoryData?.color ? `${routine.categoryData.color}d9` : '#6b7280d9',
                            color: getTextColor(routine.categoryData?.color || '#6b7280'),
                            borderLeft: `3px solid ${routine.categoryData?.color || '#6b7280'}`
                          }}
                          onClick={(e) => {
                            if (e.altKey || e.ctrlKey) {
                              onDeleteRoutine(routine.id);
                            } else {
                              onEditRoutine(routine);
                            }
                          }}
                          onKeyDown={(e) => {
                            if ((e.key === 'Enter' || e.key === ' ') && !e.ctrlKey && !e.altKey) {
                              onEditRoutine(routine);
                            }
                            if ((e.key === 'Delete' || e.key === 'Backspace') && (e.ctrlKey || e.altKey)) {
                              onDeleteRoutine(routine.id);
                            }
                          }}
                          tabIndex={0}
                          aria-label={`Routine: ${routine.title}`}
                        >
                          <div className="font-medium text-[11px] sm:text-xs">
                            {formatTime(routine.startTime)} - {formatTime(routine.endTime)}
                          </div>
                          <div className="line-clamp-2 text-[10px] sm:text-xs mt-0.5 font-medium">
                            {routine.title}
                          </div>
                          {routine.note && (
                            <div className="line-clamp-2 text-[10px] sm:text-xs mt-0.5">
                              {routine.note}
                            </div>
                          )}
                        </div>
                      );
                    })}
                  </div>
                );
              })}
            </div>
          ))}
        </div>
      </div>
    );
  };

  return (
    <Card className="shadow-sm overflow-hidden border-0">
      <CardContent className="p-0">
        <div className="overflow-auto">
          <div className="min-w-[1110px]">
            {renderWeekView()}
          </div>
          <div className="h-4"></div> {/* Add some bottom padding */}
        </div>
      </CardContent>
    </Card>
  );
}
