"use client"

import { TimeBlock } from '@/lib/types';
import { TimeBlockForm } from '@/components/time-blocks/time-block-form';

interface TimeBlockFormWrapperProps {
  isOpen: boolean;
  onClose: () => void;
  timeBlock?: TimeBlock;
  selectedDate: Date;
  updateTimeBlock: (id: string, data: any) => Promise<boolean>;
  addTimeBlock: (data: any) => Promise<any>;
  refreshTimeBlocks: () => Promise<void>;
  activeTab?: string;
}

export function TimeBlockFormWrapper({
  isOpen,
  onClose,
  timeBlock,
  selectedDate,
  updateTimeBlock,
  addTimeBlock,
  refreshTimeBlocks,
  activeTab = "grid"
}: TimeBlockFormWrapperProps) {
  return (
    <TimeBlockForm
      isOpen={isOpen}
      onClose={onClose}
      onSubmit={async (data) => {
        try {
          console.log('TimeBlockFormWrapper - Form data received:', data);

          // Ensure isTodo is properly set based on the form data and active tab
          const formData = {
            ...data,
            // If we're in the todo tab (list), automatically set isTodo to true
            // Otherwise use the value from the form or default to false
            isTodo: activeTab === "list" ? true : (data.isTodo !== undefined ? data.isTodo : false)
          };

          console.log('TimeBlockFormWrapper - Using active tab:', activeTab);
          console.log('TimeBlockFormWrapper - Final form data:', formData);

          if (timeBlock) {
            console.log('TimeBlockFormWrapper - Updating existing time block:', timeBlock.id);
            const success = await updateTimeBlock(timeBlock.id, formData);
            if (success) {
              await refreshTimeBlocks();
              return true;
            }
            return false;
          } else {
            console.log('TimeBlockFormWrapper - Adding new time block');
            const newBlock = await addTimeBlock(formData);
            if (newBlock) {
              console.log('TimeBlockFormWrapper - New block created:', newBlock);
              await refreshTimeBlocks();
              return true;
            }
            return false;
          }
        } catch (error) {
          console.error("Error handling time block operation:", error);
          return false;
        }
      }}
      timeBlock={timeBlock}
      selectedDate={selectedDate}
    />
  );
}
