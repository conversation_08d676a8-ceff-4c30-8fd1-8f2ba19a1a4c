import { NextResponse } from 'next/server';
import { timeBlockService } from '@/core/services';
import { CreateTimeBlockRequest, UpdateTimeBlockRequest } from '@/types/timeblock';
import { successResponse, errorResponse } from '@/utils/response';
import { ErrorCode } from '@/constants/error-codes';
import logger from '@/utils/logger';
import { ApiError } from '@/core/errors/api-error';

/**
 * TimeBlock controller
 */
export class TimeBlockController {
  /**
   * Get all time blocks for a user
   * @param userId User ID
   * @returns Response with time blocks or error
   */
  async getAllTimeBlocks(userId: string): Promise<NextResponse> {
    try {
      const timeBlocks = await timeBlockService.getAllTimeBlocks(userId);
      return successResponse(timeBlocks);
    } catch (error) {
      logger.error('Get time blocks controller error:', error);
      
      if (error instanceof ApiError) {
        return errorResponse(error.message, error.code, error.statusCode);
      }
      
      return errorResponse(
        'Server error',
        ErrorCode.INTERNAL_SERVER_ERROR,
        500
      );
    }
  }

  /**
   * Get time blocks for a user by date
   * @param userId User ID
   * @param date Date string (YYYY-MM-DD)
   * @returns Response with time blocks or error
   */
  async getTimeBlocksByDate(userId: string, date: string): Promise<NextResponse> {
    try {
      const timeBlocks = await timeBlockService.getTimeBlocksByDate(userId, date);
      return successResponse(timeBlocks);
    } catch (error) {
      logger.error('Get time blocks by date controller error:', error);
      
      if (error instanceof ApiError) {
        return errorResponse(error.message, error.code, error.statusCode);
      }
      
      return errorResponse(
        'Server error',
        ErrorCode.INTERNAL_SERVER_ERROR,
        500
      );
    }
  }

  /**
   * Create a new time block
   * @param userId User ID
   * @param data TimeBlock data
   * @returns Response with created time block or error
   */
  async createTimeBlock(userId: string, data: CreateTimeBlockRequest): Promise<NextResponse> {
    try {
      const timeBlock = await timeBlockService.createTimeBlock(userId, data);
      return successResponse(timeBlock, 201);
    } catch (error) {
      logger.error('Create time block controller error:', error);
      
      if (error instanceof ApiError) {
        return errorResponse(error.message, error.code, error.statusCode);
      }
      
      return errorResponse(
        'Server error',
        ErrorCode.INTERNAL_SERVER_ERROR,
        500
      );
    }
  }

  /**
   * Update a time block
   * @param id TimeBlock ID
   * @param userId User ID
   * @param data TimeBlock data
   * @returns Response with updated time block or error
   */
  async updateTimeBlock(id: string, userId: string, data: UpdateTimeBlockRequest): Promise<NextResponse> {
    try {
      const timeBlock = await timeBlockService.updateTimeBlock(id, userId, data);
      return successResponse(timeBlock);
    } catch (error) {
      logger.error('Update time block controller error:', error);
      
      if (error instanceof ApiError) {
        return errorResponse(error.message, error.code, error.statusCode);
      }
      
      return errorResponse(
        'Server error',
        ErrorCode.INTERNAL_SERVER_ERROR,
        500
      );
    }
  }

  /**
   * Delete a time block
   * @param id TimeBlock ID
   * @param userId User ID
   * @returns Response with success message or error
   */
  async deleteTimeBlock(id: string, userId: string): Promise<NextResponse> {
    try {
      await timeBlockService.deleteTimeBlock(id, userId);
      return successResponse({ message: 'Time block deleted successfully' });
    } catch (error) {
      logger.error('Delete time block controller error:', error);
      
      if (error instanceof ApiError) {
        return errorResponse(error.message, error.code, error.statusCode);
      }
      
      return errorResponse(
        'Server error',
        ErrorCode.INTERNAL_SERVER_ERROR,
        500
      );
    }
  }
}
