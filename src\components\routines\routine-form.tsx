"use client"

import { useState, useEffect, memo, useMemo } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { toast } from "sonner";
import { Dialog, DialogTitle } from "@/components/ui/dialog";
import { CustomDialogContent } from "@/components/ui/custom-dialog";
import { Form } from "@/components/ui/form";
import { VisuallyHidden } from "@/components/ui/visually-hidden";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Clock, Calendar, X, Save } from "lucide-react";
import { FormField, FormItem, FormLabel, FormControl, FormMessage } from "@/components/ui/form";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useCategories } from "@/hooks/use-categories";
import { cn } from "@/lib/utils";
import { OptimizedWeekdayPicker } from "@/components/routines/optimized-weekday-picker";

const routineSchema = z.object({
  title: z.string().min(1, "Title is required"),
  startTime: z.string().regex(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/, {
    message: "Start time must be in 24-hour format (HH:MM)",
  }),
  endTime: z.string().regex(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/, {
    message: "End time must be in 24-hour format (HH:MM)",
  }),
  days: z.array(z.number().min(0).max(6)).min(1, "Select at least one day"),
  note: z.string().optional(),
  categoryId: z.string().min(1, "Category is required"),
}).refine(data => {
  const start = data.startTime.split(":").map(Number);
  const end = data.endTime.split(":").map(Number);
  const startMinutes = start[0] * 60 + start[1];
  const endMinutes = end[0] * 60 + end[1];
  return endMinutes > startMinutes;
}, {
  message: "End time must be after start time",
  path: ["endTime"],
});

interface RoutineFormProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (data: z.infer<typeof routineSchema>) => void;
  routine?: z.infer<typeof routineSchema> & {
    id?: string;
    categoryData?: {
      name: string;
      color: string;
    };
  };
}

// WEEKDAYS array moved to OptimizedWeekdayPicker component

export function RoutineForm({ isOpen, onClose, onSubmit, routine }: RoutineFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { categories } = useCategories();

  // Get current time in HH:00 format
  const getCurrentTime = () => {
    const now = new Date();
    return `${String(now.getHours()).padStart(2, "0")}:00`;
  };

  // Get time 1 hour later in HH:00 format
  const getOneHourLater = () => {
    const now = new Date();
    now.setHours(now.getHours() + 1);
    return `${String(now.getHours()).padStart(2, "0")}:00`;
  };

  const form = useForm<z.infer<typeof routineSchema>>({
    resolver: zodResolver(routineSchema),
    defaultValues: {
      title: "",
      startTime: getCurrentTime(),
      endTime: getOneHourLater(),
      days: [],
      note: "",
      categoryId: "",
    },
  });

  // Update form values when routine changes
  useEffect(() => {
    if (routine && isOpen) {
      console.log("Setting form values from routine:", routine);
      form.reset({
        title: routine.title || "",
        startTime: routine.startTime || getCurrentTime(),
        endTime: routine.endTime || getOneHourLater(),
        days: routine.days || [],
        note: routine.note || "",
        categoryId: routine.categoryId || "",
      });
    }
  }, [routine, isOpen, form]);

  // Dialog close handler
  const handleDialogClose = () => {
    form.reset({
      title: "",
      startTime: getCurrentTime(),
      endTime: getOneHourLater(),
      days: [],
      note: "",
      categoryId: "",
    });
    onClose();
  };

  // Submit handler
  const handleSubmit = async (values: z.infer<typeof routineSchema>) => {
    setIsSubmitting(true);
    try {
      await onSubmit(values);
      handleDialogClose();
    } catch (error) {
      console.error("Error submitting routine:", error);
      toast.error(error instanceof Error ? error.message : "Failed to save routine");
    } finally {
      setIsSubmitting(false);
    }
  };

  // Get category color for the selected category
  const categoryId = form.watch('categoryId');

  // Calculate category color
  const getCategoryColor = useMemo(() => {
    if (!categoryId || !categories) return '#888888';

    const category = categories.find(c => c.id === categoryId);
    return category?.color || '#888888';
  }, [categoryId, categories]);

  return (
    <Dialog open={isOpen} onOpenChange={(open) => !open && handleDialogClose()}>
      <CustomDialogContent className="w-[95vw] max-w-sm p-0 rounded-lg bg-white dark:bg-zinc-900 shadow-xl overflow-hidden">
        <DialogTitle>
          <VisuallyHidden>{routine ? "Edit Routine" : "Add Routine"}</VisuallyHidden>
        </DialogTitle>
        {/* Header with color from selected category */}
        <div
          className="p-3 flex items-center justify-between"
          style={{
            borderBottom: `2px solid ${getCategoryColor}`,
            background: `${getCategoryColor}10`
          }}
        >
          <div className="flex items-center gap-2">
            <div
              className="w-5 h-5 rounded-full"
              style={{ backgroundColor: getCategoryColor }}
            />
            <h2 className="text-base font-semibold">
              {routine ? "Edit Routine" : "Add Routine"}
            </h2>
          </div>
          <Button
            variant="ghost"
            size="icon"
            onClick={handleDialogClose}
            className="h-7 w-7 rounded-full"
          >
            <X className="h-3.5 w-3.5" />
          </Button>
        </div>

        <Form {...form}>
          <form key={routine?.id || 'new-routine'} onSubmit={form.handleSubmit(handleSubmit)}>
            <div className="p-3 space-y-3">
              {/* Title Input */}
              <FormField
                control={form.control}
                name="title"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-sm font-medium">Title</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="Enter routine title"
                        className="h-8 text-sm"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage className="text-xs" />
                  </FormItem>
                )}
              />

              {/* Time Inputs */}
              <div>
                <FormLabel className="text-sm font-medium mb-1 flex items-center gap-1.5">
                  <Clock className="h-3 w-3 text-muted-foreground" />
                  Time
                </FormLabel>
                <div className="grid grid-cols-2 gap-2">
                  <FormField
                    control={form.control}
                    name="startTime"
                    render={({ field }) => (
                      <FormItem>
                        <div className="flex items-center gap-1.5">
                          <FormLabel className="text-xs text-muted-foreground w-8">Start</FormLabel>
                          <FormControl>
                            <Input
                              type="time"
                              className="text-xs h-7 border-input focus-visible:ring-1"
                              {...field}
                            />
                          </FormControl>
                        </div>
                        <FormMessage className="text-xs" />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name="endTime"
                    render={({ field }) => (
                      <FormItem>
                        <div className="flex items-center gap-1.5">
                          <FormLabel className="text-xs text-muted-foreground w-8">End</FormLabel>
                          <FormControl>
                            <Input
                              type="time"
                              className="text-xs h-7 border-input focus-visible:ring-1"
                              {...field}
                            />
                          </FormControl>
                        </div>
                        <FormMessage className="text-xs" />
                      </FormItem>
                    )}
                  />
                </div>
              </div>

              {/* Optimized Weekday Picker */}
              <OptimizedWeekdayPicker form={form} />

              {/* Category Picker */}
              <FormField
                control={form.control}
                name="categoryId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-sm font-medium flex items-center gap-1.5">
                      <div
                        className="w-3 h-3 rounded-full"
                        style={{ backgroundColor: getCategoryColor }}
                      />
                      Category
                    </FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      value={field.value}
                    >
                      <FormControl>
                        <SelectTrigger className="text-sm h-8 border-input focus-visible:ring-1">
                          <SelectValue placeholder="Select a category">
                            {field.value && (
                              <div className="flex items-center gap-2">
                                <div
                                  className="w-3 h-3 rounded-full flex-shrink-0"
                                  style={{
                                    backgroundColor: Array.isArray(categories) && field.value
                                      ? categories.find(c => c.id === field.value)?.color || '#6b7280'
                                      : '#6b7280'
                                  }}
                                />
                                <span className="text-sm truncate">
                                  {Array.isArray(categories) && field.value && typeof field.value === 'string'
                                    ? (categories.find(c => c.id === field.value)?.name || field.value)
                                    : ''}
                                </span>
                              </div>
                            )}
                          </SelectValue>
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent className="max-h-[180px]">
                        <div className="max-h-[160px] overflow-y-auto pr-1">
                          {Array.isArray(categories) && categories.length > 0 ? (
                            categories.map((category) => (
                              <SelectItem
                                key={category.id}
                                value={category.id}
                                className="flex items-center gap-2"
                              >
                                <div className="flex items-center gap-2">
                                  <div
                                    className="w-3 h-3 rounded-full flex-shrink-0"
                                    style={{ backgroundColor: category.color }}
                                  />
                                  <span className="text-sm truncate">{category.name}</span>
                                </div>
                              </SelectItem>
                            ))
                          ) : (
                            <div className="p-2 text-xs text-muted-foreground text-center">
                              {Array.isArray(categories) && categories.length === 0
                                ? "No categories found"
                                : "Loading categories..."}
                            </div>
                          )}
                        </div>
                      </SelectContent>
                    </Select>
                    <FormMessage className="text-xs" />
                  </FormItem>
                )}
              />

              {/* Note Input */}
              <FormField
                control={form.control}
                name="note"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-sm font-medium">Notes (optional)</FormLabel>
                    <FormControl>
                      <Textarea
                        placeholder="Add notes about this routine"
                        className="min-h-[60px] text-sm resize-none border-input focus-visible:ring-1"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage className="text-xs" />
                  </FormItem>
                )}
              />
            </div>

            {/* Action Buttons */}
            <div className="border-t p-2 flex justify-end gap-2 bg-muted/5">
              <Button
                type="button"
                variant="outline"
                onClick={handleDialogClose}
                className="h-8 text-xs font-medium"
              >
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={isSubmitting}
                className={cn(
                  "h-8 text-xs font-medium",
                  isSubmitting ? "opacity-70" : ""
                )}
                style={{
                  backgroundColor: getCategoryColor,
                  color: '#ffffff',
                }}
              >
                {isSubmitting ? (
                  <div className="flex items-center gap-1.5">
                    <div className="w-3 h-3 border-2 border-white border-t-transparent rounded-full animate-spin" />
                    <span>Saving...</span>
                  </div>
                ) : (
                  <>
                    <Save className="h-3 w-3 mr-1" />
                    {routine ? "Update" : "Create"}
                  </>
                )}
              </Button>
            </div>
          </form>
        </Form>
      </CustomDialogContent>
    </Dialog>
  );
}
