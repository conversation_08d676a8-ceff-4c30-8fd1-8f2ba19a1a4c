/**
 * Server-only utilities
 *
 * IMPORTANT: Only import this file in server components or API routes
 * This file provides server-side utilities and types in an organized manner
 */

// ===== Database =====
// Database connection
export { default as connectDB } from '@/config/database';

// Database models
export { User, TimeBlock, Category } from '@/db/models';

// Database model types
export type { IUser, ITimeBlock, ICategory, IUserPreferences } from '@/db/models';

// ===== Authentication =====
// Auth utilities
export { verifyAuth, getToken } from '@/utils/auth';
export { authMiddleware } from '@/app/api/_middleware/auth.middleware';

// ===== Business Logic =====
// Controllers
export {
  authController,
  timeBlockController,
  categoryController
} from '@/core/controllers';
