"use client"

import { useState } from 'react';
import { Card } from '@/components/ui/card';
import { TimeBlock, TimeBlockFormData } from '@/lib/types';
import { useCategories } from '@/hooks/use-categories';
import { cn } from '@/lib/utils';
import { TimeB<PERSON>Header } from './TimeBlockHeader';
import { TimeBlockStatus } from './TimeBlockStatus';
import { TimeBlockNote } from './TimeBlockNote';
import { TimeBlockForm } from '../time-block-form-component/TimeBlockForm';
import { DeleteDialog } from './DeleteDialog';

interface TimeBlockItemProps {
  timeBlock: TimeBlock;
  onUpdate: (id: string, data: Partial<TimeBlock>) => void;
  onDelete: (id: string) => void;
  onEdit?: (timeBlock: TimeBlock) => void;
}

export function TimeBlockItem({
  timeBlock,
  onUpdate,
  onDelete,
  onEdit
}: TimeBlockItemProps) {
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
  const [isEditFormOpen, setIsEditFormOpen] = useState(false);
  const { categories } = useCategories();

  // Get color for a time block's category
  const getCategoryColor = (block: TimeBlock) => {
    if (block.categoryData?.color) {
      return block.categoryData.color;
    }

    const categoryId = typeof block.category === 'object' && block.category !== null
      ? (block.category as any).id || block.category
      : block.category;

    const category = categories.find(c => c.id === categoryId);
    return category?.color || "#6b7280";
  };

  // Handle edit button click
  const handleEdit = () => {
    if (onEdit) {
      onEdit(timeBlock);
    } else {
      setIsEditFormOpen(true);
    }
  };

  // Handle form update
  const handleUpdate = async (data: TimeBlockFormData): Promise<boolean> => {
    try {
      const idToUse = timeBlock.id || (timeBlock as any)._id?.toString();
      if (!idToUse || idToUse === 'undefined') {
        console.error('Cannot update time block: Invalid ID');
        return false;
      }

      // Remove the MongoDB ObjectId validation as it might be a UUID in local storage
      // if (!/^[0-9a-fA-F]{24}$/.test(idToUse)) {
      //   throw new Error('Cannot update time block: Invalid ID format');
      // }

      onUpdate(idToUse, data);
      setIsEditFormOpen(false);
      return true;
    } catch (error) {
      console.error('Error updating time block:', error);
      return false;
    }
  };

  return (
    <>
      <Card
        className={cn(
          "group overflow-hidden shadow-sm hover:shadow transition-all duration-200 h-[220px] w-full cursor-pointer todo-card border-2 flex flex-col",
          timeBlock.isTodo && timeBlock.isCompleted && "bg-green-100 dark:bg-green-900/30"
        )}
        style={{
          borderColor: timeBlock.isTodo && timeBlock.isCompleted
            ? 'rgba(34, 197, 94, 0.8)' // Brighter green for completed todos (80% opacity)
            : `${getCategoryColor(timeBlock)}80` // 80% opacity for category color
        }}
        onClick={handleEdit}
      >
        <div
          className="w-full card-top-bar"
          style={{
            height: '4px',
            backgroundColor: timeBlock.isTodo && timeBlock.isCompleted
              ? 'rgb(34, 197, 94)' // Solid green for completed todos
              : getCategoryColor(timeBlock) // Solid category color
          }}
        />
        <div className="p-2 flex-1 overflow-hidden">
          <div className="flex flex-col h-full">
            <TimeBlockHeader timeBlock={timeBlock} />
            <TimeBlockStatus
              timeBlock={timeBlock}
              onToggleTodo={() => onUpdate(timeBlock.id, { isCompleted: !timeBlock.isCompleted })}
              onEdit={handleEdit}
              onDelete={() => setIsDeleteDialogOpen(true)}
              categoryColor={getCategoryColor(timeBlock)}
            />
            <TimeBlockNote
              note={timeBlock.note}
              isCompleted={timeBlock.isTodo && timeBlock.isCompleted}
            />
          </div>
        </div>
      </Card>

      <DeleteDialog
        isOpen={isDeleteDialogOpen}
        onClose={() => setIsDeleteDialogOpen(false)}
        onConfirm={() => {
          onDelete(timeBlock.id);
          setIsDeleteDialogOpen(false);
        }}
      />

      <TimeBlockForm
        isOpen={isEditFormOpen}
        onClose={() => setIsEditFormOpen(false)}
        onSubmit={handleUpdate}
        timeBlock={timeBlock}
        selectedDate={new Date(timeBlock.date)}
      />
    </>
  );
}
