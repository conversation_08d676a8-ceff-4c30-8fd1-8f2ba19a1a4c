"use client"

import { useState, useEffect } from "react";
import { toast } from "sonner";
import useS<PERSON> from "swr";
import { useAuth } from "./use-auth";

import { ApiRoutine, FormRoutine, formToApiRoutine } from "@/types/routine";


// Custom fetcher for SWR that handles errors and adds debugging
const fetcher = async (url: string) => {
  try {
    const response = await fetch(url);
    if (!response.ok) {
      const error = await response.json();
      console.error('Fetch error:', error);
      throw new Error(error.message || 'An error occurred while fetching data');
    }
    const data = await response.json();
    console.log('Fetched routines:', data); // Debug log

    // Validate that data is an array
    if (!Array.isArray(data)) {
      console.error('Invalid data format:', data);
      throw new Error('Invalid data format received from server');
    }

    return data;
  } catch (error) {
    console.error('Fetcher error:', error);
    throw error;
  }
};

export function useRoutines() {
  const { isAuthenticated } = useAuth();
  const [isLoading, setIsLoading] = useState(false);

  // Use a longer deduping interval to prevent excessive API calls
  const { data: routines = [], mutate, error } = useSWR<ApiRoutine[]>(
    isAuthenticated ? "/api/routines" : null,
    fetcher,
    {
      dedupingInterval: 60000, // Increase to 60 seconds to reduce API calls
      revalidateOnFocus: false, // Disable revalidation on focus
      revalidateOnMount: true,
      onError: (err) => {
        console.error('SWR Error:', err);
        if (err.message === 'Unauthorized') {
          toast.error('Please log in to view routines');
        } else {
          toast.error('Failed to load routines. Please try again.');
        }
      },
      shouldRetryOnError: false, // Prevent retrying on auth errors
      revalidateIfStale: false, // Disable automatic background revalidation
      focusThrottleInterval: 60000, // Throttle focus events
      loadingTimeout: 5000, // Increase loading timeout
      errorRetryInterval: 10000, // Longer retry interval
      errorRetryCount: 2 // Limit retry attempts
    }
  );

  const createRoutine = async (data: Omit<FormRoutine, "id">) => {
    try {
      setIsLoading(true);
      const response = await fetch("/api/routines", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(formToApiRoutine(data)),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || "Failed to create routine");
      }

      const newRoutine = await response.json();
      await mutate();
      toast.success("Routine created successfully");
      return newRoutine;
    } catch (error) {
      toast.error(error instanceof Error ? error.message : "Failed to create routine");
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const updateRoutine = async (id: string, data: Partial<FormRoutine>) => {
    try {
      setIsLoading(true);
      const response = await fetch(`/api/routines/${id}`, {
        method: "PUT",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(formToApiRoutine(data as FormRoutine)),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || "Failed to update routine");
      }

      const updatedRoutine = await response.json();
      await mutate();
      toast.success("Routine updated successfully");
      return updatedRoutine;
    } catch (error) {
      toast.error(error instanceof Error ? error.message : "Failed to update routine");
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  const deleteRoutine = async (id: string) => {
    try {
      setIsLoading(true);
      const response = await fetch(`/api/routines/${id}`, {
        method: "DELETE",
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || "Failed to delete routine");
      }

      await mutate();
      toast.success("Routine deleted successfully");
    } catch (error) {
      toast.error(error instanceof Error ? error.message : "Failed to delete routine");
      throw error;
    } finally {
      setIsLoading(false);
    }
  };

  // Determine if we're in a loading state
  const isLoadingState = isLoading || (!error && !routines.length && !routines);

  return {
    routines,
    isLoading: isLoadingState,
    error,
    createRoutine,
    updateRoutine,
    deleteRoutine,
    // Use a function that prevents multiple rapid calls
    refetch: () => {
      console.log("Manual refetch requested");
      return mutate(undefined, { revalidate: true });
    }
  };
}
