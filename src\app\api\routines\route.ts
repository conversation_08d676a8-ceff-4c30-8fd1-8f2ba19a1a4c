import { NextRequest, NextResponse } from "next/server";
import { routineSchema } from "@/db/schemas/routine.schema";
import connectDB from "@/config/database";
import { verifyAuth } from "@/utils/auth";
import mongoose from "mongoose";

export async function POST(req: NextRequest) {
  try {
    // Verify user is authenticated
    const { authenticated, user } = verifyAuth(req);
    if (!authenticated || !user) {
      return NextResponse.json({ message: "Unauthorized" }, { status: 401 });
    }

    // Parse and validate request body
    const body = await req.json();
    await connectDB();

    // Get the category to store its data
    const category = await mongoose.connection
      .collection("categories")
      .findOne({ _id: new mongoose.Types.ObjectId(body.categoryId) });

    if (!category) {
      return NextResponse.json({ message: "Category not found" }, { status: 400 });
    }

    // Validate with categoryId
    const validated = routineSchema.parse({
      ...body,
      userId: user.id,
      categoryId: body.categoryId,
      categoryData: {
        name: category.name,
        color: category.color
      }
    });

    // Create routine with category data
    const routine = await mongoose.connection.collection("routines").insertOne({
      ...validated,
      categoryData: {
        name: category.name,
        color: category.color
      },
      createdAt: new Date(),
      updatedAt: new Date(),
    });

    return NextResponse.json({
      id: routine.insertedId.toString(),
      ...validated,
      categoryData: {
        name: category.name,
        color: category.color
      }
    });
  } catch (error) {
    console.error("Error creating routine:", error);
    if (error instanceof Error) {
      return NextResponse.json({ message: error.message }, { status: 400 });
    }
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    );
  }
}

export async function GET(req: NextRequest) {
  try {
    // Verify user is authenticated
    const { authenticated, user } = verifyAuth(req);
    if (!authenticated || !user) {
      return NextResponse.json({ message: "Unauthorized" }, { status: 401 });
    }

    await connectDB();
    
    // Get all routines for the user
    const routines = await mongoose.connection
      .collection("routines")
      .find({ userId: user.id })
      .toArray();

    // Get all unique category IDs from the routines
    const categoryIds = Array.from(new Set(routines.map(r => r.categoryId)));

    // Fetch all referenced categories in one query
    const categories = await mongoose.connection
      .collection("categories")
      .find({ _id: { $in: categoryIds.map(id => new mongoose.Types.ObjectId(id)) } })
      .toArray();

    // Create a map of category data by ID for easy lookup
    const categoryMap = new Map(
      categories.map(cat => [cat._id.toString(), {
        name: cat.name,
        color: cat.color
      }])
    );

    // Add category data to each routine
    return NextResponse.json(routines.map(r => ({
      ...r,
      id: r._id?.toString?.() || r.id,
      _id: undefined,
      categoryData: categoryMap.get(r.categoryId) || {
        name: "Category",
        color: "#6b7280"
      }
    })));
  } catch (error) {
    console.error("Error fetching routines:", error);
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    );
  }
}
