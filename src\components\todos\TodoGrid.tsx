"use client"

import { TimeBlock, Category } from "@/lib/types";
import { format, parseISO, isToday, isTomorrow, isPast, isThisWeek, isSameDay } from "date-fns";
import { CheckCircle2, Circle, Clock, Edit, Trash, AlertTriangle, Calendar } from "lucide-react";
import { But<PERSON> } from "../ui/button";
import { Card } from "../ui/card";
import { Badge } from "../ui/badge";
import { cn } from "@/lib/utils";
import { getCategoryColor } from "../time-blocks/utils";
import { getTimeBlockCategoryStyles, getTimeBlockCategoryColor } from "../time-blocks/category-styles";
import { useCategories } from "@/hooks/use-categories";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "../ui/tooltip";
import { Ta<PERSON>, <PERSON><PERSON>Content, Ta<PERSON>List, TabsTrigger } from "../ui/tabs";
import { useRef, useEffect, useState } from "react";

// Helper function to get category name
function getCategoryName(block: TimeBlock, categories: Category[]) {
  if (block.categoryData?.name) {
    return block.categoryData.name;
  }

  const categoryId = typeof block.category === 'object' && block.category !== null
    ? (block.category as any).id || block.category
    : block.category;

  const category = categories.find((c) => c.id === categoryId);
  return category?.name || null;
}

interface TodoGridProps {
  todos: TimeBlock[];
  onToggleComplete: (todo: TimeBlock) => void;
  onEdit: (todo: TimeBlock) => void;
  onDelete: (todo: TimeBlock) => void;
}

export function TodoGrid({ todos, onToggleComplete, onEdit, onDelete }: TodoGridProps) {
  // Always call hooks at the top
  const firstLoadRef = useRef(true);
  const [activeTab, setActiveTab] = useState("today");
  const { categories } = useCategories();
  
  useEffect(() => {
    // Set first load to false after initial render
    if (firstLoadRef.current) {
      firstLoadRef.current = false;
    }
  }, []);

  // Used for preventing duplicate tasks
  const uniqueTodoMap = new Map();

  // Filter only real todos and respect the showInMyTodos flag for routine tasks
  // Also handle time-based filtering for more precise display
  const filteredTodos = todos.filter(todo => {
    // Make sure it's a todo
    if (!todo.isTodo) return false;
    
    // If it's a routine task (has routineId), check if it should be shown in MyTodos
    if (todo.routineId) {
      // Either it's explicitly marked to show in MyTodos, or it's today's task
      return todo.showInMyTodos === true || isToday(parseISO(todo.date));
    }
    
    // For time blocks with specific times, ensure we only show them at their exact time
    if (todo.startTime && todo.endTime) {
      // This is a time-specific block, so we keep it as is
      return true;
    }
    
    // For regular todos (not routine tasks and not time-specific), always show them
    return true;
  });

  // Group todos by date and remove duplicates (for routine tasks)
  const groupedTodos = filteredTodos.reduce((acc, todo) => {
    const date = format(parseISO(todo.date), 'yyyy-MM-dd');
    if (!acc[date]) {
      acc[date] = [];
    }
    
    // Create a more robust unique key to prevent duplicates
    // For routine tasks, combine date + routineId + startTime (if any) to ensure uniqueness
    // For regular todos, use the id which should already be unique
    let uniqueKey;
    if (todo.routineId) {
      // For routine tasks, include time info if available to distinguish different time blocks
      uniqueKey = `${date}-${todo.routineId}-${todo.startTime || ''}-${todo.endTime || ''}`;
    } else {
      uniqueKey = `${date}-${todo.id}`;
    }
    
    // Only add if we haven't seen this todo before
    if (!uniqueTodoMap.has(uniqueKey)) {
      uniqueTodoMap.set(uniqueKey, true);
      acc[date].push(todo);
    }
    
    return acc;
  }, {} as Record<string, TimeBlock[]>);

  // Sort dates
  const sortedDates = Object.keys(groupedTodos).sort((a, b) => {
    const dateA = parseISO(a);
    const dateB = parseISO(b);
    return dateA.getTime() - dateB.getTime();
  });
  
  // Categorize todos into previous, today, and upcoming
  const today = format(new Date(), 'yyyy-MM-dd');
  
  // Create sections for previous, today, and upcoming todos
  const previousTodos: Record<string, TimeBlock[]> = {};
  const todayTodos: Record<string, TimeBlock[]> = {};
  const upcomingTodos: Record<string, TimeBlock[]> = {};
  
  // Distribute todos into the appropriate sections
  sortedDates.forEach(date => {
    if (date < today) {
      previousTodos[date] = groupedTodos[date];
    } else if (date === today) {
      todayTodos[date] = groupedTodos[date];
    } else {
      upcomingTodos[date] = groupedTodos[date];
    }
  });

  // Empty state
  if (filteredTodos.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[200px] text-center p-8">
        <svg className="w-16 h-16 text-gray-300 mb-4" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" d="M9 12h6m-3-3v6m9 2a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
        <h3 className="text-lg font-medium mb-2">No todos yet</h3>
        <p className="text-muted-foreground text-sm max-w-xs">
          Create a todo to start tracking your tasks.
        </p>
      </div>
    );
  }

  // Helper function to render a todo section with improved styling
  const renderTodoSection = (title: string, todosSection: Record<string, TimeBlock[]>, emptyMessage: string) => {
    const sortedDates = Object.keys(todosSection).sort((a, b) => {
      const dateA = parseISO(a);
      const dateB = parseISO(b);
      return dateA.getTime() - dateB.getTime();
    });

    if (sortedDates.length === 0) {
      return (
        <div className="p-6 text-center bg-gradient-to-b from-muted/10 to-muted/20 rounded-xl border border-border/30 flex flex-col items-center justify-center h-36 backdrop-blur-sm shadow-sm hover:shadow-md transition-all">
          <p className="text-sm text-muted-foreground/80 font-medium bg-gradient-to-r from-muted-foreground/70 to-muted-foreground/90 bg-clip-text text-transparent">{emptyMessage}</p>
        </div>
      );
    }

    return (
      <div className="flex flex-col gap-4 p-1 overflow-hidden h-[calc(100vh-200px)] md:h-[calc(100vh-170px)]">
        <div className="h-full space-y-5 overflow-y-auto pr-1 no-scrollbar custom-scrollbar md:pb-0 pb-20">
          {sortedDates.map((date) => (
            <div key={date} className="rounded-xl overflow-hidden">
              <h3 className="text-sm font-medium mb-3 text-muted-foreground flex items-center bg-gradient-to-br from-card/60 to-card/80 p-2 rounded-lg shadow-sm backdrop-blur-sm border border-border/30">
                <Calendar className="h-3.5 w-3.5 mr-1.5 opacity-80" />
                <span className="bg-gradient-to-r from-foreground/80 to-foreground/90 bg-clip-text text-transparent">
                  {format(parseISO(date), "EEEE, MMMM d")}
                </span>
              </h3>
              <div className="flex flex-col gap-2.5">
                {todosSection[date].map((todo) => {
                  const todoDate = parseISO(todo.date);
                  // Use todo.id if available, otherwise fallback to date + note + index
                  const key = todo.id || `${date}-${todo.note}`;
                  return (
                    <div key={key}>
                      {renderTodoItem(todo, todoDate)}
                    </div>
                  );
                })}
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  };

  // Helper function to render a todo item with improved design
  const renderTodoItem = (todo: TimeBlock, todoDate: Date) => {
    const isOverdue = isPast(todoDate) && !isToday(todoDate) && !todo.isCompleted;
    const isUpcoming = !isToday(todoDate) && !isPast(todoDate);
    
    const categoryColor = getTimeBlockCategoryColor(todo, categories);
    
    return (
      <div
        className={cn(
          "border rounded-xl p-4 transition-all group relative",
          "hover:shadow-md hover:border-primary/50 hover:bg-gradient-to-b hover:from-primary/5 hover:to-primary/2",
          "shadow-sm",
          todo.isCompleted ? 
            "bg-gradient-to-br from-green-50/90 to-green-50/50 dark:from-green-950/40 dark:to-green-950/20 border-green-200 dark:border-green-900/50" : 
            isOverdue ? 
              "bg-gradient-to-br from-red-50/90 to-red-50/50 dark:from-red-950/40 dark:to-red-950/20 border-red-200 dark:border-red-900/50" : 
              "bg-gradient-to-br from-card/90 to-card/95 border-border/50 backdrop-blur-sm"
        )}
        style={{
          // Add a subtle left accent with the category color
          borderLeftColor: categoryColor,
          borderLeftWidth: '4px',
          boxShadow: `0 4px 6px -1px rgba(0, 0, 0, 0.05), 0 2px 4px -1px rgba(0, 0, 0, 0.03), inset 0 2px 4px rgba(255, 255, 255, 0.1)`
        }}
      >
        <div className="flex">
          <Button
            variant="ghost" 
            size="icon"
            className={cn(
              "h-6 w-6 rounded-full p-0 mr-2.5 flex-shrink-0 transition-all duration-200",
              todo.isCompleted ? 
                "text-green-600 hover:text-green-700 hover:bg-green-100 dark:text-green-500 dark:hover:bg-green-900/30" : 
                "text-muted-foreground hover:text-primary hover:bg-primary/15"
            )}
            onClick={() => onToggleComplete(todo)}
          >
            {todo.isCompleted ? (
              <CheckCircle2 className="h-4.5 w-4.5" />
            ) : (
              <Circle className="h-4.5 w-4.5" />
            )}
          </Button>

          <div className="flex-1 min-w-0">
            <div className="flex justify-between items-start gap-2">
              <h4 className={cn(
                "text-sm font-medium break-words leading-tight", 
                todo.isCompleted ? "line-through text-muted-foreground" : "text-slate-800 dark:text-slate-200"
              )}>
                {todo.note}
              </h4>
            </div>
            
            <div className="mt-2 flex flex-wrap items-center gap-1.5">
              {/* Show category badge if available */}
              {(todo.category || todo.categoryData) && (
                <Badge 
                  variant="outline" 
                  className="text-[11px] py-0.5 px-2.5 h-6 rounded-full font-medium shadow-sm"
                  style={{
                    backgroundColor: `${categoryColor}20`,
                    color: categoryColor,
                    borderColor: `${categoryColor}40`,
                    boxShadow: `0 1px 2px ${categoryColor}10`,
                  }}
                >
                  {todo.categoryData?.name || getCategoryName(todo, categories) || 'Uncategorized'}
                </Badge>
              )}

              {/* Routine badge */}
              {todo.routineId && (
                <Badge 
                  variant="secondary" 
                  className="text-[10px] py-0 px-2 h-5 rounded-full font-medium shadow-sm bg-purple-100/80 text-purple-700 border-purple-200/80 dark:bg-purple-900/40 dark:text-purple-400 dark:border-purple-800/60"
                >
                  <Clock className="h-2.5 w-2.5 mr-0.5" />
                  Routine
                </Badge>
              )}
              
              {/* Date status badges */}
              {isPast(todoDate) && !isToday(todoDate) && !todo.isCompleted && (
                <Badge 
                  variant="outline" 
                  className="text-[10px] py-0 px-2 h-5 rounded-full font-medium shadow-sm bg-red-50/80 text-red-700 border-red-100/80 dark:bg-red-900/30 dark:text-red-400 dark:border-red-800/60"
                >
                  <AlertTriangle className="h-2.5 w-2.5 mr-0.5" />
                  Overdue
                </Badge>
              )}

              {/* Upcoming badge */}
              {isUpcoming && !isOverdue && (
                <Badge 
                  variant="outline" 
                  className="text-[10px] py-0 px-2 h-5 rounded-full font-medium shadow-sm bg-amber-50/80 text-amber-700 border-amber-100/80 dark:bg-amber-900/30 dark:text-amber-400 dark:border-amber-800/60"
                >
                  <Calendar className="h-2.5 w-2.5 mr-0.5" />
                  Upcoming
                </Badge>
              )}
            </div>
          </div>

          {/* Action buttons with improved hover states */}
          <div className="flex items-center gap-1.5 ml-3 opacity-80 group-hover:opacity-100 transition-opacity">
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-7 w-7 p-0 rounded-full text-slate-500 hover:text-primary hover:bg-primary/10 transition-colors"
                    onClick={(e) => {
                      e.stopPropagation();
                      onEdit(todo);
                    }}
                  >
                    <Edit className="h-3.5 w-3.5" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>Edit Todo</TooltipContent>
              </Tooltip>
            </TooltipProvider>
            
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-7 w-7 p-0 rounded-full text-slate-500 hover:text-destructive hover:bg-destructive/10 transition-colors"
                    onClick={(e) => {
                      e.stopPropagation();
                      onDelete(todo);
                    }}
                  >
                    <Trash className="h-3.5 w-3.5" />
                  </Button>
                </TooltipTrigger>
                <TooltipContent>Delete</TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>
        </div>
      </div>
    );
  };

  // Helper function to render tab headers with proper color styling
  const TabHeader = ({ color, text }: { color: string; text: string }) => {
    const dotColors = {
      amber: "bg-amber-500",
      green: "bg-green-500",
      blue: "bg-blue-500"
    };

    return (
      <div className="flex items-center gap-2">
        <div className={cn("w-2 h-2 rounded-full", dotColors[color as keyof typeof dotColors])} />
        <span className="bg-gradient-to-r from-slate-800/90 to-slate-800 dark:from-slate-200/90 dark:to-slate-200 bg-clip-text text-transparent font-medium">
          {text}
        </span>
      </div>
    );
  };

  return (
    <div className="animate-in fade-in duration-500 h-[calc(100vh-150px)] md:h-auto" 
      style={{ 
        opacity: firstLoadRef.current ? 0 : 1,
        transition: 'opacity 0.3s ease-in-out'
      }}>
      {/* Mobile view with tabs */}
      <div className="md:hidden h-full flex flex-col">
        <Tabs defaultValue="today" className="w-full h-full flex flex-col" value={activeTab} onValueChange={setActiveTab}>

          <TabsList className="grid w-full grid-cols-3 mb-4 p-1 h-12 bg-gradient-to-br from-card/60 to-card/80 shadow-sm backdrop-blur-sm border border-border/30">
            <TabsTrigger 
              value="previous" 
              className="data-[state=active]:bg-white dark:data-[state=active]:bg-slate-950 data-[state=active]:text-slate-900 dark:data-[state=active]:text-slate-50 data-[state=active]:shadow-sm transition-all">
              <TabHeader color="amber" text="Previous" />
            </TabsTrigger>
            <TabsTrigger 
              value="today"
              className="data-[state=active]:bg-white dark:data-[state=active]:bg-slate-950 data-[state=active]:text-slate-900 dark:data-[state=active]:text-slate-50 data-[state=active]:shadow-sm transition-all">
              <TabHeader color="green" text="Today" />
            </TabsTrigger>
            <TabsTrigger 
              value="upcoming"
              className="data-[state=active]:bg-white dark:data-[state=active]:bg-slate-950 data-[state=active]:text-slate-900 dark:data-[state=active]:text-slate-50 data-[state=active]:shadow-sm transition-all">
              <TabHeader color="blue" text="Upcoming" />
            </TabsTrigger>
          </TabsList>
          <div className="flex-1 overflow-hidden">
            <TabsContent value="previous" className="mt-0 h-full">
              {renderTodoSection("Previous", previousTodos, "No previous todos")}
            </TabsContent>
            <TabsContent value="today" className="mt-0 h-full">
              {renderTodoSection("Today", todayTodos, "No todos for today")}
            </TabsContent>
            <TabsContent value="upcoming" className="mt-0 h-full">
              {renderTodoSection("Upcoming", upcomingTodos, "No upcoming todos")}
            </TabsContent>
          </div>
        </Tabs>
      </div>

      {/* Desktop view with grid */}
      <div className="hidden md:grid grid-cols-3 gap-6 min-h-[500px] h-full">
        <div className="flex flex-col h-full">
          <h2 className="text-lg font-semibold mb-3 px-3 py-2 flex items-center bg-gradient-to-br from-card/60 to-card/80 rounded-lg shadow-sm backdrop-blur-sm border border-border/30">
            <div className="w-2 h-2 rounded-full bg-amber-500 mr-2.5"></div>
            <span className="bg-gradient-to-r from-slate-800/90 to-slate-800 dark:from-slate-200/90 dark:to-slate-200 bg-clip-text text-transparent">Previous</span>
          </h2>
          {renderTodoSection("Previous", previousTodos, "No previous todos")} 
        </div>
        
        <div className="flex flex-col h-full">
          <h2 className="text-lg font-semibold mb-3 px-3 py-2 flex items-center bg-gradient-to-br from-card/60 to-card/80 rounded-lg shadow-sm backdrop-blur-sm border border-border/30">
            <div className="w-2 h-2 rounded-full bg-green-500 mr-2.5"></div>
            <span className="bg-gradient-to-r from-slate-800/90 to-slate-800 dark:from-slate-200/90 dark:to-slate-200 bg-clip-text text-transparent">Today</span>
          </h2>
          {renderTodoSection("Today", todayTodos, "No todos for today")} 
        </div>
        
        <div className="flex flex-col h-full">
          <h2 className="text-lg font-semibold mb-3 px-3 py-2 flex items-center bg-gradient-to-br from-card/60 to-card/80 rounded-lg shadow-sm backdrop-blur-sm border border-border/30">
            <div className="w-2 h-2 rounded-full bg-blue-500 mr-2.5"></div>
            <span className="bg-gradient-to-r from-slate-800/90 to-slate-800 dark:from-slate-200/90 dark:to-slate-200 bg-clip-text text-transparent">Upcoming</span>
          </h2>
          {renderTodoSection("Upcoming", upcomingTodos, "No upcoming todos")} 
        </div>
      </div>
    </div>
  );
}
