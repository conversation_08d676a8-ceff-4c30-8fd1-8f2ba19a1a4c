import { z } from "zod";

export const routineSchema = z.object({
  id: z.string().optional(),
  userId: z.string(),
  categoryId: z.string(),
  title: z.string().min(1, "Title is required"),
  startTime: z.string(),
  endTime: z.string(),
  days: z.array(z.number().min(0).max(6)), // 0 = Sunday, 6 = Saturday
  note: z.string().optional(),
  isEnabled: z.boolean().default(true),
  createdAt: z.date().optional(),
  updatedAt: z.date().optional(),
});

export type Routine = z.infer<typeof routineSchema>;
