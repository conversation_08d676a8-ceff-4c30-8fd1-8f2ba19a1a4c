import { NextRequest } from 'next/server';
import * as jwt from 'jsonwebtoken';
import crypto from 'crypto';
import bcrypt from 'bcrypt';
import { JWT_SECRET } from '@/config/auth';
import { JwtPayload, AuthResult } from '@/types/auth';
import { ApiError } from '@/core/errors/api-error';
import { ErrorCode } from '@/constants/error-codes';

/**
 * Get JWT token from request
 * @param req Next.js request
 * @returns JWT token or null
 */
export function getToken(req: NextRequest): string | null {
  // Try to get token from Authorization header
  const authHeader = req.headers.get('authorization');
  if (authHeader && authHeader.startsWith('Bearer ')) {
    return authHeader.substring(7);
  }

  // If not in header, try cookies from the request
  return req.cookies.get('token')?.value || null;
}

/**
 * Verify authentication
 * @param req Next.js request
 * @returns Authentication result
 */
export function verifyAuth(req: NextRequest): AuthResult {
  try {
    const token = getToken(req);

    if (!token) {
      return { authenticated: false };
    }

    const decoded = (jwt as any).verify(token, JWT_SECRET) as JwtPayload;
    return {
      authenticated: true,
      user: decoded
    };
  } catch (error) {
    // Check error type by name since instanceof might not work with the import style
    if (error instanceof Error) {
      if (error.name === 'TokenExpiredError') {
        throw ApiError.unauthorized('Token expired', ErrorCode.TOKEN_EXPIRED);
      } else if (error.name === 'JsonWebTokenError') {
        throw ApiError.unauthorized('Invalid token', ErrorCode.INVALID_TOKEN);
      }
    }

    return { authenticated: false };
  }
}

/**
 * Generate JWT token
 * @param payload Token payload
 * @param expiresIn Token expiration time
 * @returns JWT token
 */
export function generateToken(payload: JwtPayload, expiresIn: string): string {
  // Use any type to bypass TypeScript checking
  return (jwt as any).sign(payload, JWT_SECRET, { expiresIn });
}

// Word lists for generating memorable access keys
const ADJECTIVES = [
  "ancient",
  "brave",
  "clever",
  "daring",
  "eager",
  "fancy",
  "gentle",
  "humble",
  "icy",
  "joyful",
  "kindhearted",
  "lively",
  "mighty",
  "noble",
  "obedient",
  "polite",
  "quick",
  "radiant",
  "silent",
  "tender",
  "ugly",
  "vast",
  "witty",
  "yellowish",
  "zealous",
  "artistic",
  "basic",
  "careful",
  "dusty",
  "elegant",
  "fearless",
  "graceful",
  "hungry",
  "invisible",
  "jolly",
  "kind",
  "lonely",
  "mysterious",
  "noisy",
  "open",
  "peaceful",
  "quiet",
  "rich",
  "shiny",
  "tiny",
  "unusual",
  "vast",
  "worried",
  "youthful",
  "zesty",
  "awesome",
  "bitter",
  "charming",
  "delicate",
  "empty",
  "filthy",
  "gentle",
  "happy",
  "innocent",
  "jealous",
  "kindly",
  "lovely",
  "magical",
  "nervous",
  "optimistic",
  "patient",
  "quirky",
  "royal",
  "sleepy",
  "tidy",
  "unique",
  "vibrant",
  "weary",
  "young",
  "zippy",
  "angry",
  "broad",
  "cloudy",
  "daring",
  "eager",
  "faithful",
  "gloomy",
  "helpful",
  "intelligent",
  "joyful",
  "keen",
  "lazy",
  "modern",
  "natural",
  "old-fashioned",
  "powerful",
  "quick-witted",
  "rare",
  "sharp",
  "tall",
  "unfair",
  "vivid",
  "weak",
  "yielding",
  "zany",
  "adventurous",
  "bouncy",
  "calm",
  "dangerous",
  "efficient",
  "fancy",
  "gentle",
  "harsh",
  "ideal",
  "jolly",
  "kindhearted",
  "lucky",
  "massive",
  "narrow",
  "ordinary",
  "pleasant",
  "quiet",
  "rapid",
  "shiny",
  "tough",
  "useful",
  "valuable",
  "warm",
  "youthful",
  "zigzag",
  "alert",
  "busy",
  "cheerful",
  "distant",
  "earthy",
  "frozen",
  "glowing",
  "honest",
  "icy",
  "jolly",
  "kindly",
  "loud",
  "muddy",
  "nice",
  "open-minded",
  "proper",
  "realistic",
  "smart",
  "thoughtful",
  "upright",
  "vast",
  "wild",
  "yearly",
  "zesty",
  "anxious",
  "brisk",
  "careful",
  "daring",
  "eager",
  "fancy",
  "gentle",
  "happy",
  "intelligent",
  "jolly",
  "kind",
  "loyal",
  "mild",
  "nervous",
  "optimistic",
  "patient",
  "quiet",
  "rare",
  "silent",
  "tall",
  "useful",
  "vivid",
  "warm",
  "young",
  "zany",
  "amazing",
  "bitter",
  "cold",
  "dusty",
  "early",
  "fair",
  "grand",
  "hungry",
  "inspired",
  "joyful",
  "keen",
  "lucky",
  "modest",
  "neat",
  "polite",
  "quick",
  "royal",
  "shy",
  "tasty",
  "unusual",
  "vast",
  "wide",
  "yellow",
  "zealous",
  "active",
  "brave",
  "clean",
  "deep",
  "elegant",
  "fast",
  "good",
  "honest",
  "icy",
  "kind",
  "large",
  "modern",
  "nice",
  "old",
  "proud",
  "quiet",
  "rich",
  "smart",
  "tiny",
  "ugly",
  "vast",
  "wise",
  "young",
  "zesty",
  "cool",
  "dark",
  "fair",
  "gentle",
  "hot",
  "kind",
  "lazy",
  "noble",
  "pure",
  "small",
  "tall",
  
  "abrasive","agreeable","amused","anguished","apprehensive","ashamed","athletic","attentive","authentic","authoritative",
  "belligerent","beneficial","blissful","boisterous","braveheart","bright-eyed","bubbly","calculating","careless","casual",
  "charming2","cheerful2","childlike","civil","clumsy","comical","compassionate","competent","composed","conceited",
  "confident","considerate","constant","content","convincing","courageous","courteous","crafty2","creative","critical",
  "cunning2","curious2","cynical","dashing","decisive","dedicated","delightful","dependable","descriptive","determined",
  "diligent","diplomatic","discreet","dynamic","earnest","ecstatic","efficient2","elated","eloquent","energetic",
  "engaging","enlightened","enthusiastic","epic","ethical","euphoric","exuberant","fabulous","factual","faithful2",
  "fanciful","fascinating","fearless2","ferocious","fiery","flamboyant","flexible","focused","fortunate","friendly",
  "frugal","funny","fuzzy2","generous","gentle2","gleaming","gloomy2","graceful2","gregarious","grounded",
  "handsome","happy2","hardworking","harmonious","honest2","hopeful","hospitable","humorous","idealistic","imaginative",
  "impressive","industrious","influential","ingenious","inquisitive","insightful","inspiring","intelligent2","intuitive","inventive",
  "jubilant2","judicious","keen2","kind2","knowledgeable","lively2","logical","lovable","loving2","loyal2",
  "magnetic","majestic","marvelous","methodical2","meticulous","modest2","motivated","mysterious2","neat2","noble2",
  "observant","optimistic2","orderly2","organized","outgoing","passionate","peaceful2","persistent","persuasive","playful2",
  "polished2","positive","powerful2","practical2","precise2","proactive","productive","proud2","punctual","quick2",
  "quiet2","rational","reliable","remarkable","resilient","resourceful","respectful","responsible","responsive","romantic2",
  "sage2","satisfied","secure2","selfless","sensible","sincere2","skillful","smart2","sociable","spirited",
  "spiritual","spontaneous","steadfast","strategic","strong2","studious","supportive","surprising","sympathetic","talented",
  "tactful","tenacious","thoughtful2","thrifty","tolerant","trustworthy","truthful2","unique2","upbeat","valiant",
  "versatile","vigilant","vigorous","vivacious","warm2","watchful","well-behaved","well-read","wise2","witty2",
  "wonderful","youthful2","zealous2","zesty2","accomplished","admirable","adventurous2","affectionate","agile","alert2",
  "alluring","amazing2","amiable","analytical","animated","appreciative","artistic2","assertive","astonishing","attentive2",
  "audacious","authentic2","awful","balanced2","beautiful","benevolent","blazing2","bold2","brilliant","bubbly2",
  "calm2","capable","careful2","carefree","caring","charming3","cheerful3","chipper","civil2","clean",
  "clever2","coherent","compassionate2","competent2","composed2","concise","confident2","conscientious","considerate2","consistent",
  "content2","convincing2","cool2","cooperative","cordial","courageous2","courteous2","creative2","credible","curious3"


];


const NOUNS = [
  "airport2","apartment","arch","arena","artwork","astronaut","atom","aunt","author","avenue2",
  "backyard","ball","bank","barn2","barrel","bat","beach2","bear2","beast","bench",
  "bicycle","bird2","blade","boat2","book2","bridge2","brook2","building","bus","butterfly2",
  "cabin2","cactus","cake","camera","camp2","canal2","candle","canyon2","cape","car2",
  "card","cart2","castle2","cat2","cave2","chair2","chalk","channel2","chapel","chart",
  "cheese","chimney2","church2","circle","city2","cliff2","clock2","cloud2","club","coach",
  "coast","cobweb","coffee","cottage2","court","cow2","cradle","crane","creek2","creature",
  "crescent","crow2","cup","curve","cushion","cyclone","dairy","dam","dance2","dawn2",
  "day","deck","desert2","desk","device","dome2","door2","drift","drum2","duck2",
  "dune2","eagle2","earth2","edge","egg","engine2","era","estate","ethics","event",
  "farm2","feather","field2","finger","fire2","fish2","flag","forest2","fountain","frame",
  "garden2","gate2","gem2","glass","globe","goblet","goose2","grave","grape2","grass2",
  "grotto","harbor2","hill2","hollow","home2","horizon2","house2","hut2","ice","island2",
  "jet","jungle2","king","kingdom2","kitchen","lake2","lamp2","land","lane2","leaf2",
  "library2","light2","lion2","log","lodge","market2","mask","meadow2","moon2","mountain2",
  "mound","museum","nest","night","oak","ocean2","office","orb","orchard2","palace2",
  "park2","path2","pebble","pen","pencil","pier2","planet2","plateau2","pond2","pool",
  "port2","prairie2","quarry","quilt","rain2","raven2","relic","ridge2","river2","road2",
  "rock2","rose2","roof2","root2","sail","school2","sea","seashore","shadow2","shrine2",
  "sky2","snow2","song","spike","spring2","star2","stone2","stream2","street","sun2",
  "swamp2","temple2","throne","tower2","trail2","tree2","valley2","village2","vine2","wall2",
  "water2","wave2","wheel2","wind2","window2","wood","yard","yarn","zephyr","zone",
  "river",
  "mountain",
  "ocean",
  "forest",
  "desert",
  "valley",
  "hill",
  "cloud",
  "star",
  "moon",
  "sun",
  "rain",
  "snow",
  "fire",
  "earth",
  "tree",
  "flower",
  "grass",
  "stone",
  "rock",
  "sand",
  "wave",
  "tide",
  "dawn",
  "dusk",
  "bird",
  "fish",
  "wolf",
  "bear",
  "deer",
  "eagle",
  "hawk",
  "fox",
  "lion",
  "tiger",
  "whale",
  "shark",
  "dolphin",
  "turtle",
  "rabbit",
  "horse",
  "cat",
  "dog",
  "mouse",
  "crow",
  "sheep",
  "goat",
  "frog",
  "snake",
  "apple",
  "banana",
  "cherry",
  "mango",
  "orange",
  "pear",
  "grape",
  "plum",
  "peach",
  "berry",
  "breeze",
  "storm",
  "thunder",
  "lightning",
  "rainbow",
  "fog",
  "mist",
  "glacier",
  "island",
  "cave",
  "riverbank",
  "cliff",
  "meadow",
  "grove",
  "jungle",
  "orchard",
  "swamp",
  "beach",
  "canyon",
  "plain",
  "pond",
  "stream",
  "brook",
  "shore",
  "bay",
  "reef",
  "coral",
  "volcano",
  "crater",
  "village",
  "town",
  "city",
  "capital",
  "palace",
  "castle",
  "tower",
  "temple",
  "fort",
  "bridge",
  "garden",
  "park",
  "field",
  "road",
  "path",
  "gate",
  "wall",
  "fence",
  "market",
  "house",
  "home",
  "cabin",
  "hut",
  "camp",
  "farm",
  "barn",
  "stable",
  "shed",
  "port",
  "ship",
  "boat",
  "raft",
  "car",
  "train",
  "plane",
  "rocket",
  "wheel",
  "cart",
  "engine",
  "motor",
  "mirror",
  "clock",
  "lamp",
  "table",
  "chair",
  "door",
  "window",
  "bed",
  "floor",
  "roof",
  "book",
  "pen",
  "paper",
  "scroll",
  "letter",
  "map",
  "compass",
  "coin",
  "crown",
  "sword",
  "shield",
  "armor",
  "ring",
  "gem",
  "stone",
  "crystal",
  "jewel",
  "pearl",
  "shell",
  "bone",
  "cloth",
  "rope",
  "chain",
  "key",
  "lock",
  "drum",
  "flute",
  "horn",
  "bell",
  "pipe",
  "dream",
  "hope",
  "fear",
  "joy",
  "peace",
  "war",
  "love",
  "hate",
  "truth",
  "lie",
  "song",
  "tune",
  "rhythm",
  "dance",
  "art",
  "craft",
  "play",
  "story",
  "poem",
  "myth",
  "spirit",
  "soul",
  "heart",
  "mind",
  "body",
  "voice",
  "hand",
  "eye",
  "face",
  "smile",
  "shadow",
  "light",
  "flame",
  "dust",
  "leaf",
  "branch",
  "root",
  "seed",
  "fruit",
  "flower",
  "rain",
  "stone",
];


/**
 * Generate a cryptographically secure random access key
 * Format: 8 words separated by dashes + 10-digit number (e.g., 'swift-river-ancient-brave-clever-daring-eager-fancy-1234567890')
 * Provides approximately (330^8) * (10^10) combinations = massive entropy space
 * Uses crypto.randomBytes for cryptographically secure randomness
 * @returns Unique access key
 */
export function generateAccessKey(): string {
  // Use crypto.randomBytes for cryptographically secure randomness
  const randomBytes = crypto.randomBytes(16); // Increased to 16 bytes for more entropy

  // Combine both word lists for more variety
  const allWords = [...ADJECTIVES, ...NOUNS];
  const words: string[] = [];

  // Generate 8 random words
  for (let i = 0; i < 8; i++) {
    const wordIndex = randomBytes[i] % allWords.length;
    words.push(allWords[wordIndex]);
  }

  // Generate a 10-digit number using remaining bytes and additional entropy
  const additionalBytes = crypto.randomBytes(8);
  let numberStr = '';

  for (let i = 0; i < 10; i++) {
    const byteIndex = i % 8;
    const digit = additionalBytes[byteIndex] % 10;
    numberStr += digit.toString();
  }

  // Ensure the number doesn't start with 0 (make it exactly 10 digits)
  if (numberStr[0] === '0') {
    numberStr = '1' + numberStr.slice(1);
  }

  return `${words.join('-')}-${numberStr}`;
}

/**
 * Validate access key format
 * @param key Access key to validate
 * @returns True if valid format
 */
export function isValidAccessKeyFormat(key: string): boolean {
  if (!key || typeof key !== 'string') return false;

  const parts = key.split('-');
  if (parts.length !== 9) return false; // 8 words + 1 number

  // Extract words and number
  const words = parts.slice(0, 8);
  const number = parts[8];

  // Combine both word lists for validation
  const allWords = [...ADJECTIVES, ...NOUNS];

  // Check if all words are in our word lists
  const areValidWords = words.every(word =>
    allWords.includes(word.toLowerCase())
  );

  // Check if number is exactly 10 digits
  const isValidNumber = /^\d{10}$/.test(number);

  return areValidWords && isValidNumber;
}

/**
 * Legacy validation function for backward compatibility
 * Validates old format: adjective-noun-number (e.g., 'swift-river-42')
 * @param key Access key to validate
 * @returns True if valid legacy format
 */
export function isValidLegacyAccessKeyFormat(key: string): boolean {
  if (!key || typeof key !== 'string') return false;

  const parts = key.split('-');
  if (parts.length !== 3) return false;

  const [adjective, noun, number] = parts;

  // Check if adjective and noun are in our word lists
  const isValidAdjective = ADJECTIVES.includes(adjective.toLowerCase());
  const isValidNoun = NOUNS.includes(noun.toLowerCase());

  // Check if number is 2 digits
  const isValidNumber = /^\d{2}$/.test(number);

  return isValidAdjective && isValidNoun && isValidNumber;
}

/**
 * Hash an access key using bcrypt
 * @param accessKey Plain text access key
 * @returns Hashed access key
 */
export async function hashAccessKey(accessKey: string): Promise<string> {
  const saltRounds = 12; // High salt rounds for security
  return bcrypt.hash(accessKey.toLowerCase(), saltRounds);
}

/**
 * Compare an access key with its hash
 * @param accessKey Plain text access key
 * @param hashedKey Hashed access key from database
 * @returns True if keys match
 */
export async function compareAccessKey(accessKey: string, hashedKey: string): Promise<boolean> {
  return bcrypt.compare(accessKey.toLowerCase(), hashedKey);
}

/**
 * Generate recovery codes for account backup
 * @param count Number of recovery codes to generate (default: 10)
 * @returns Array of recovery codes
 */
export function generateRecoveryCodes(count: number = 10): string[] {
  const codes: string[] = [];

  for (let i = 0; i < count; i++) {
    // Generate 8-character alphanumeric codes
    const code = crypto.randomBytes(4).toString('hex').toUpperCase();
    codes.push(code);
  }

  return codes;
}

/**
 * Hash recovery codes using bcrypt
 * @param codes Array of plain text recovery codes
 * @returns Array of hashed recovery codes
 */
export async function hashRecoveryCodes(codes: string[]): Promise<string[]> {
  const saltRounds = 12;
  const hashedCodes = await Promise.all(
    codes.map(code => bcrypt.hash(code, saltRounds))
  );
  return hashedCodes;
}

/**
 * Compare a recovery code with hashed codes
 * @param code Plain text recovery code
 * @param hashedCodes Array of hashed recovery codes
 * @returns Index of matching code or -1 if no match
 */
export async function compareRecoveryCode(code: string, hashedCodes: string[]): Promise<number> {
  for (let i = 0; i < hashedCodes.length; i++) {
    const isMatch = await bcrypt.compare(code, hashedCodes[i]);
    if (isMatch) {
      return i;
    }
  }
  return -1;
}

/**
 * Validate recovery code format
 * @param code Recovery code to validate
 * @returns True if valid format
 */
export function isValidRecoveryCodeFormat(code: string): boolean {
  if (!code || typeof code !== 'string') return false;
  // 8-character alphanumeric code
  return /^[A-F0-9]{8}$/.test(code.toUpperCase());
}
