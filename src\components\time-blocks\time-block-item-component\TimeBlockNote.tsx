"use client"

import { cn } from '@/lib/utils';

interface TimeBlockNoteProps {
  note: string;
  isCompleted?: boolean;
}

export function TimeBlockNote({ note, isCompleted }: TimeBlockNoteProps) {
  return (
    <div className="text-sm overflow-y-auto flex-grow h-[calc(100%)] max-h-[100px]">
      <p className={cn(
        "font-medium break-words whitespace-normal",
        isCompleted && "text-green-800 dark:text-green-300"
      )}>
        {note}
      </p>
    </div>
  );
}
