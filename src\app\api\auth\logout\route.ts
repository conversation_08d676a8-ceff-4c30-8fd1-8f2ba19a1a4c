import { NextRequest } from 'next/server';
import { authController } from '@/core/controllers';
import logger from '@/utils/logger';
import { errorResponse } from '@/utils/response';
import { ErrorCode } from '@/constants/error-codes';

// Tell Next.js this route should always be dynamically rendered
export const dynamic = 'force-dynamic';

/**
 * POST /api/auth/logout - Logout a user
 */
export async function POST(req: NextRequest) {
  try {
    return authController.logout();
  } catch (error) {
    logger.error('Logout error:', error);
    return errorResponse(
      'An unexpected error occurred',
      ErrorCode.INTERNAL_SERVER_ERROR,
      500
    );
  }
}
