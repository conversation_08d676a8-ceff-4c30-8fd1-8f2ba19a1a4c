Stack trace:
Frame         Function      Args
0007FFFFBBC0  00021005FEBA (000210285F48, 00021026AB6E, 000000000000, 0007FFFFAAC0) msys-2.0.dll+0x1FEBA
0007FFFFBBC0  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFBE98) msys-2.0.dll+0x67F9
0007FFFFBBC0  000210046832 (000210285FF9, 0007FFFFBA78, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFFBBC0  000210068F86 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28F86
0007FFFFBBC0  0002100690B4 (0007FFFFBBD0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x290B4
0007FFFFBEA0  00021006A49D (0007FFFFBBD0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A49D
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFA71CE0000 ntdll.dll
7FFA718E0000 KERNEL32.DLL
7FFA6F0D0000 KERNELBASE.dll
7FFA70600000 USER32.dll
7FFA6EE70000 win32u.dll
000210040000 msys-2.0.dll
7FFA6FC80000 GDI32.dll
7FFA6F540000 gdi32full.dll
7FFA6EEA0000 msvcp_win.dll
7FFA6F7D0000 ucrtbase.dll
7FFA6FD30000 advapi32.dll
7FFA6FBA0000 msvcrt.dll
7FFA71A00000 sechost.dll
7FFA71740000 RPCRT4.dll
7FFA6E370000 CRYPTBASE.DLL
7FFA6F4A0000 bcryptPrimitives.dll
7FFA6FDF0000 IMM32.DLL
