/**
 * Error codes for API responses
 * These are used to provide consistent error codes across the API
 */
export enum ErrorCode {
  // General errors
  BAD_REQUEST = 'BAD_REQUEST',
  UNAUTHORIZED = 'UNAUTHORIZED',
  FORBIDDEN = 'FORBIDDEN',
  NOT_FOUND = 'NOT_FOUND',
  CONFLICT = 'CONFLICT',
  INTERNAL_SERVER_ERROR = 'INTERNAL_SERVER_ERROR',
  
  // Authentication errors
  INVALID_CREDENTIALS = 'INVALID_CREDENTIALS',
  INVALID_TOKEN = 'INVALID_TOKEN',
  TOKEN_EXPIRED = 'TOKEN_EXPIRED',
  ACCOUNT_LOCKED = 'ACCOUNT_LOCKED',
  IP_RATE_LIMITED = 'IP_RATE_LIMITED',
  TOO_MANY_REQUESTS = 'TOO_MANY_REQUESTS',
  INVALID_RECOVERY_CODE = 'INVALID_RECOVERY_CODE',
  RECOVERY_CODE_USED = 'RECOVERY_CODE_USED',
  
  // User errors
  USER_NOT_FOUND = 'USER_NOT_FOUND',
  USER_ALREADY_EXISTS = 'USER_ALREADY_EXISTS',
  INVALID_PASSWORD = 'INVALID_PASSWORD',
  DUPLICATE_ACCESS_KEY = 'DUPLICATE_ACCESS_KEY',
  
  // TimeBlock errors
  TIMEBLOCK_NOT_FOUND = 'TIMEBLOCK_NOT_FOUND',
  INVALID_TIMEBLOCK_DATA = 'INVALID_TIMEBLOCK_DATA',
  
  // Category errors
  CATEGORY_NOT_FOUND = 'CATEGORY_NOT_FOUND',
  INVALID_CATEGORY_DATA = 'INVALID_CATEGORY_DATA',
  
  // Database errors
  DATABASE_ERROR = 'DATABASE_ERROR',
  
  // Validation errors
  VALIDATION_ERROR = 'VALIDATION_ERROR',
}
