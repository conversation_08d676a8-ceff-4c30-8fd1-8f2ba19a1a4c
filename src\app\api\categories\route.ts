import { NextRequest } from 'next/server';
import { validateCreateCategory } from '@/app/api/_validators/category.validator';
import { categoryController } from '@/core/controllers';
import { authMiddleware } from '@/app/api/_middleware/auth.middleware';
import logger from '@/utils/logger';
import { errorResponse } from '@/utils/response';
import { ErrorCode } from '@/constants/error-codes';
import { verifyAuth } from '@/utils/auth';

// Tell Next.js this route should always be dynamically rendered
export const dynamic = 'force-dynamic';

/**
 * GET /api/categories - Get all categories for a user
 */
export async function GET(req: NextRequest) {
  try {
    // Check authentication
    const authResponse = authMiddleware(req);
    if (authResponse) return authResponse;

    // Get user from token
    const { user } = verifyAuth(req);
    if (!user) {
      return errorResponse('Authentication required', ErrorCode.UNAUTHORIZED, 401);
    }

    // Get all categories
    return categoryController.getAllCategories(user.id);
  } catch (error) {
    logger.error('Get categories error:', error);
    return errorResponse(
      'An unexpected error occurred',
      ErrorCode.INTERNAL_SERVER_ERROR,
      500
    );
  }
}

/**
 * POST /api/categories - Create a new category
 */
export async function POST(req: NextRequest) {
  try {
    // Check authentication
    const authResponse = authMiddleware(req);
    if (authResponse) return authResponse;

    // Get user from token
    const { user } = verifyAuth(req);
    if (!user) {
      return errorResponse('Authentication required', ErrorCode.UNAUTHORIZED, 401);
    }

    // Validate request
    const validationResult = await validateCreateCategory(req);
    if ('status' in validationResult) {
      return validationResult;
    }

    // Create category
    return categoryController.createCategory(user.id, validationResult.data);
  } catch (error) {
    logger.error('Create category error:', error);
    return errorResponse(
      'An unexpected error occurred',
      ErrorCode.INTERNAL_SERVER_ERROR,
      500
    );
  }
}
