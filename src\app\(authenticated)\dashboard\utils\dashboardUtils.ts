import { format, isToday, startOfWeek, endOfWeek, isSameDay, parseISO, isWithinInterval, startOfMonth, endOfMonth } from 'date-fns';
import { TimeBlock } from '@/lib/types';

// Type for analytics period
export type AnalyticsPeriod = 'daily' | 'weekly' | 'monthly';

// Type for category stats
export interface CategoryStat {
  categoryId: string;
  category: string;
  color: string;
  minutes: number;
  hours: number;
  remainingMinutes: number;
}

// Get period description based on selected analytics period
export const getPeriodDescription = (analyticsPeriod: 'daily' | 'weekly' | 'monthly', selectedDate: Date) => {
  if (analyticsPeriod === 'daily') {
    return isToday(selectedDate) ? 'today' : format(selectedDate, 'MMM d');
  } else if (analyticsPeriod === 'weekly') {
    const weekStart = startOfWeek(selectedDate, { weekStartsOn: 1 });
    const weekEnd = endOfWeek(selectedDate, { weekStartsOn: 1 });
    return `${format(weekStart, 'MMM d')} - ${format(weekEnd, 'MMM d')}`;
  } else {
    return format(selectedDate, 'MMMM yyyy');
  }
};

// Get color based on category with opacity support
export const getCategoryColor = (categoryInput: string | object, categories: any[], opacity: number = 1) => {
  // Handle different category input types
  let categoryColor = '#6b7280'; // Default gray color

  try {
    // First check if we have a valid categories array
    if (!categories || !Array.isArray(categories)) {
      console.warn('Categories is not an array or is undefined');
      // Return default color with opacity if needed
      return opacity === 1 ? categoryColor : `rgba(107, 114, 128, ${opacity})`;
    }

    if (typeof categoryInput === 'string') {
      // Case 1: Input is a category name
      const categoryByName = categories.find(c => c && c.name === categoryInput);
      if (categoryByName?.color) {
        categoryColor = categoryByName.color;
      } else {
        // Case 2: Input might be a category ID
        const categoryById = categories.find(c => c && c.id === categoryInput);
        if (categoryById?.color) {
          categoryColor = categoryById.color;
        }
      }
    } else if (typeof categoryInput === 'object' && categoryInput !== null) {
      // Case 3: Input is a category object
      if ((categoryInput as any).color) {
        categoryColor = (categoryInput as any).color;
      } else if ((categoryInput as any).id) {
        const categoryById = categories.find(c => c && c.id === (categoryInput as any).id);
        if (categoryById?.color) {
          categoryColor = categoryById.color;
        }
      } else if ((categoryInput as any).categoryData?.color) {
        // Case 4: Input has categoryData directly
        categoryColor = (categoryInput as any).categoryData.color;
      }
    }

    // If opacity is 1, return original color
    if (opacity === 1) return categoryColor;

    // Convert hex to rgba for opacity
    const hex = categoryColor.replace('#', '');
    const r = parseInt(hex.substr(0, 2), 16);
    const g = parseInt(hex.substr(2, 2), 16);
    const b = parseInt(hex.substr(4, 2), 16);
    return `rgba(${r}, ${g}, ${b}, ${opacity})`;
  } catch (error) {
    console.error('Error in getCategoryColor:', error);
    // Return default color with opacity if needed
    return opacity === 1 ? categoryColor : `rgba(107, 114, 128, ${opacity})`;
  }
};

// Get text color based on background color with enhanced contrast
export const getTextColor = (bgColor: string) => {
  try {
    const hex = bgColor.replace('#', '');
    const r = parseInt(hex.substr(0, 2), 16);
    const g = parseInt(hex.substr(2, 2), 16);
    const b = parseInt(hex.substr(4, 2), 16);

    // Using WCAG contrast ratio calculation for better accessibility
    const luminance = (0.2126 * r + 0.7152 * g + 0.0722 * b) / 255;
    return luminance > 0.6 ? 'text-gray-900' : 'text-white';
  } catch (e) {
    return 'text-white';
  }
};

// Calculate duration in minutes from HH:MM time strings
export const calculateDurationMinutes = (startTime: string, endTime: string): number => {
  try {
    const start = startTime.split(':').map(Number);
    const end = endTime.split(':').map(Number);

    const startMinutes = start[0] * 60 + start[1];
    const endMinutes = end[0] * 60 + end[1];

    return endMinutes - startMinutes;
  } catch (error) {
    console.error('Error calculating duration:', error);
    return 0;
  }
};

// Get blocks based on the selected analytics period
export const getBlocksForPeriod = (
  analyticsPeriod: AnalyticsPeriod,
  filteredBlocks: TimeBlock[],
  weeklyBlocks: TimeBlock[],
  monthlyBlocks: TimeBlock[]
): TimeBlock[] => {
  return analyticsPeriod === 'daily' ? filteredBlocks :
         analyticsPeriod === 'weekly' ? weeklyBlocks :
         monthlyBlocks;
};

// Calculate total time tracked for a set of blocks
export const calculateTotalTimeTracked = (blocks: TimeBlock[]): number => {
  if (!blocks.length) return 0;

  let totalMinutes = 0;

  blocks.forEach(block => {
    try {
      const duration = calculateDurationMinutes(block.startTime, block.endTime);
      totalMinutes += duration;
    } catch (error) {
      console.error('Error calculating total time:', error);
    }
  });

  return totalMinutes;
};

// Calculate category statistics
export const calculateCategoryStats = (
  blocks: TimeBlock[],
  categories: any[]
): CategoryStat[] => {
  if (!blocks.length) return [];

  try {
    // Use a map to track minutes by category ID
    const categoryMinutes: Record<string, number> = {};
    // Keep track of category data for each category ID
    const categoryInfo: Record<string, { name: string; color: string }> = {};

    blocks.forEach(block => {
      try {
        const duration = calculateDurationMinutes(block.startTime, block.endTime);

        // Get the category ID
        const categoryId = typeof block.category === 'object' && block.category !== null
          ? (block.category as any)._id || (block.category as any).id || block.category
          : block.category;

        // Initialize if not exists
        if (!categoryMinutes[categoryId]) {
          categoryMinutes[categoryId] = 0;

          // Store category info for later use
          // First try to get from block.categoryData
          if (block.categoryData) {
            categoryInfo[categoryId] = {
              name: block.categoryData.name,
              color: block.categoryData.color
            };
          } else {
            // Fallback to finding in categories array
            if (Array.isArray(categories)) {
              const category = categories.find(c => c && c.id === categoryId);
              if (category) {
                categoryInfo[categoryId] = {
                  name: category.name,
                  color: category.color
                };
              } else {
                // Default if category not found
                categoryInfo[categoryId] = {
                  name: 'No Category',
                  color: '#6b7280'
                };
              }
            } else {
              // Default if categories is not an array
              categoryInfo[categoryId] = {
                name: 'No Category',
                color: '#6b7280'
              };
            }
          }
        }

        // Add duration to the category
        categoryMinutes[categoryId] += duration;
      } catch (blockError) {
        console.error('Error processing block in calculateCategoryStats:', blockError);
        // Continue with next block
      }
    });

    // Convert to array with all needed info
    return Object.entries(categoryMinutes)
      .map(([categoryId, minutes]) => ({
        categoryId,
        category: categoryInfo[categoryId]?.name || 'No Category',
        color: categoryInfo[categoryId]?.color || '#6b7280',
        minutes,
        hours: Math.floor(minutes / 60),
        remainingMinutes: minutes % 60
      }))
      // Sort by minutes in descending order
      .sort((a, b) => b.minutes - a.minutes);
  } catch (error) {
    console.error('Error in calculateCategoryStats:', error);
    return [];
  }
};

// Filter time blocks by date
export const filterTimeBlocksByDate = (
  timeBlocks: TimeBlock[] | undefined,
  selectedDate: Date
): {
  dailyBlocks: TimeBlock[];
  weeklyBlocks: TimeBlock[];
  monthlyBlocks: TimeBlock[];
} => {
  if (!timeBlocks) {
    return {
      dailyBlocks: [],
      weeklyBlocks: [],
      monthlyBlocks: []
    };
  }

  // Daily blocks - for the selected date
  const dailyBlocks = timeBlocks.filter(block => {
    const blockDate = parseISO(block.date);
    return isSameDay(blockDate, selectedDate);
  });

  // Weekly blocks - for the current week of the selected date
  const weekStart = startOfWeek(selectedDate, { weekStartsOn: 1 });
  const weekEnd = endOfWeek(selectedDate, { weekStartsOn: 1 });
  const weeklyBlocks = timeBlocks.filter(block => {
    const blockDate = parseISO(block.date);
    return isWithinInterval(blockDate, { start: weekStart, end: weekEnd });
  });

  // Monthly blocks - for the current month of the selected date
  const monthStart = startOfMonth(selectedDate);
  const monthEnd = endOfMonth(selectedDate);
  const monthlyBlocks = timeBlocks.filter(block => {
    const blockDate = parseISO(block.date);
    return isWithinInterval(blockDate, { start: monthStart, end: monthEnd });
  });

  return {
    dailyBlocks,
    weeklyBlocks,
    monthlyBlocks
  };
};
