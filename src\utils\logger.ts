/**
 * Logger utility
 * Provides consistent logging across the application
 */

// Define log levels
type LogLevel = 'debug' | 'info' | 'warn' | 'error';

// Define log function
interface LogFunction {
  (message: string, meta?: any): void;
}

// Define logger interface
interface Logger {
  debug: LogFunction;
  info: LogFunction;
  warn: LogFunction;
  error: LogFunction;
}

/**
 * Simple logger implementation
 * In a production environment, this would be replaced with a more robust solution
 */
class ConsoleLogger implements Logger {
  private readonly isDev = process.env.NODE_ENV !== 'production';

  /**
   * Log a debug message
   * Only logs in development environment
   */
  debug(message: string, meta?: any): void {
    if (this.isDev) {
      console.debug(`[DEBUG] ${message}`, meta ? meta : '');
    }
  }

  /**
   * Log an info message
   */
  info(message: string, meta?: any): void {
    console.info(`[INFO] ${message}`, meta ? meta : '');
  }

  /**
   * Log a warning message
   */
  warn(message: string, meta?: any): void {
    console.warn(`[WARN] ${message}`, meta ? meta : '');
  }

  /**
   * Log an error message
   */
  error(message: string, meta?: any): void {
    console.error(`[ERROR] ${message}`, meta ? meta : '');
  }
}

// Export singleton instance
const logger = new ConsoleLogger();
export default logger;
