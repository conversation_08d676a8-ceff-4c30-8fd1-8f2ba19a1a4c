import { UserRepository } from './user.repository';
import { TimeBlockRepository } from './timeblock.repository';
import { CategoryRepository } from './category.repository';
import { RoutineRepository } from './routine.repository';

// Export singleton instances
export const userRepository = new UserRepository();
export const timeBlockRepository = new TimeBlockRepository();
export const categoryRepository = new CategoryRepository();
export const routineRepository = new RoutineRepository();

// Export repository classes
export { UserRepository, TimeBlockRepository, CategoryRepository, RoutineRepository };
