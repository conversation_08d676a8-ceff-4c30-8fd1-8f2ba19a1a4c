import { NextRequest } from 'next/server';
import { validateCreateTimeBlock } from '@/app/api/_validators/timeblock.validator';
import { timeBlockController } from '@/core/controllers';
import { authMiddleware } from '@/app/api/_middleware/auth.middleware';
import logger from '@/utils/logger';
import { errorResponse } from '@/utils/response';
import { ErrorCode } from '@/constants/error-codes';
import { verifyAuth } from '@/utils/auth';

// Tell Next.js this route should always be dynamically rendered
export const dynamic = 'force-dynamic';

/**
 * GET /api/timeblocks - Get all time blocks for a user
 */
export async function GET(req: NextRequest) {
  try {
    // Check authentication
    const authResponse = authMiddleware(req);
    if (authResponse) return authResponse;

    // Get user from token
    const { user } = verifyAuth(req);
    if (!user) {
      return errorResponse('Authentication required', ErrorCode.UNAUTHORIZED, 401);
    }

    // Get all time blocks
    return timeBlockController.getAllTimeBlocks(user.id);
  } catch (error) {
    logger.error('Get time blocks error:', error);
    return errorResponse(
      'An unexpected error occurred',
      ErrorCode.INTERNAL_SERVER_ERROR,
      500
    );
  }
}

/**
 * POST /api/timeblocks - Create a new time block
 */
export async function POST(req: NextRequest) {
  try {
    // Check authentication
    const authResponse = authMiddleware(req);
    if (authResponse) return authResponse;

    // Get user from token
    const { user } = verifyAuth(req);
    if (!user) {
      return errorResponse('Authentication required', ErrorCode.UNAUTHORIZED, 401);
    }

    // Validate request
    const validationResult = await validateCreateTimeBlock(req);
    if ('status' in validationResult) {
      return validationResult;
    }

    // Create time block
    return timeBlockController.createTimeBlock(user.id, validationResult.data);
  } catch (error) {
    logger.error('Create time block error:', error);
    return errorResponse(
      'An unexpected error occurred',
      ErrorCode.INTERNAL_SERVER_ERROR,
      500
    );
  }
}
