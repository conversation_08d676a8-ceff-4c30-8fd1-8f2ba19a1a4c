"use client"

import { memo } from "react";
import { ToggleGroup, ToggleGroupItem } from "@/components/ui/toggle-group";
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { UseFormReturn } from "react-hook-form";
import { Calendar } from "lucide-react";

// Define weekdays outside component to prevent recreation on each render
const WEEKDAYS = [
  { value: "0", label: "Su" },
  { value: "1", label: "Mo" },
  { value: "2", label: "Tu" },
  { value: "3", label: "We" },
  { value: "4", label: "Th" },
  { value: "5", label: "Fr" },
  { value: "6", label: "Sa" },
];

interface OptimizedWeekdayPickerProps {
  form: UseFormReturn<any>;
}

// Simple component for day selection
function OptimizedWeekdayPickerComponent({ form }: OptimizedWeekdayPickerProps) {
  return (
    <div>
      <FormLabel className="text-sm font-medium mb-1 flex items-center gap-1.5">
        <Calendar className="h-3 w-3 text-muted-foreground" />
        Days
      </FormLabel>
      <FormField
        control={form.control}
        name="days"
        render={({ field }) => {
          // Convert number array to string array for ToggleGroup
          const stringValues = field.value?.map(String) || [];

          // Direct value change handler to avoid unnecessary processing
          const handleValueChange = (value: string[]) => {
            // Convert back to number array
            field.onChange(value.map(Number));
          };

          return (
            <FormItem>
              <FormControl>
                <ToggleGroup
                  type="multiple"
                  variant="outline"
                  className="grid grid-cols-7 gap-1"
                  value={stringValues}
                  onValueChange={handleValueChange}
                >
                  {WEEKDAYS.map((day) => (
                    <ToggleGroupItem
                      key={day.value}
                      value={day.value}
                      variant="outline"
                      className="h-7 w-7 p-0 text-xs transition-colors data-[state=on]:bg-white data-[state=on]:text-black data-[state=on]:border-primary dark:data-[state=on]:bg-white dark:data-[state=on]:text-black"
                    >
                      {day.label}
                    </ToggleGroupItem>
                  ))}
                </ToggleGroup>
              </FormControl>
              <FormMessage className="text-xs" />
            </FormItem>
          );
        }}
      />
    </div>
  );
}

// Export component
export const OptimizedWeekdayPicker = memo(OptimizedWeekdayPickerComponent);
