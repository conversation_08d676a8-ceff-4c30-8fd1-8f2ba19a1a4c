# NoteHour - Time Tracker & Note App

A modern web application for tracking time and taking notes throughout your day with customizable time blocks.

## Project Structure

The project follows a clean architecture with clear separation of concerns:

```
/
├── app/                  # Next.js app directory (pages and API routes)
│   ├── api/              # API routes
│   ├── auth/             # Authentication pages
│   ├── dashboard/        # Dashboard pages
│   ├── home/             # Home pages
│   ├── settings/         # Settings pages
│   └── globals.css       # Global styles
├── components/           # React components
│   ├── layouts/          # Layout components
│   ├── time-blocks/      # Time block related components
│   ├── ui/               # UI components (shadcn/ui)
│   └── ...               # Other components
├── hooks/                # Custom React hooks
├── lib/                  # Client-side utilities
│   ├── types.ts          # Client-side type definitions
│   ├── utils.ts          # Client-side utility functions
│   └── server-utils.ts   # Server-side utilities (only import in server components)
├── public/               # Static assets
└── src/                  # Backend code
    ├── api/              # API layer
    │   ├── middlewares/  # API middlewares
    │   └── validators/   # Request validation
    ├── core/             # Core business logic
    │   ├── controllers/  # Controllers
    │   ├── services/     # Services
    │   ├── repositories/ # Data access
    │   └── errors/       # Error handling
    ├── db/               # Database
    │   ├── models/       # Mongoose models
    │   └── schemas/      # Mongoose schemas
    ├── config/           # Configuration
    ├── types/            # TypeScript type definitions
    ├── utils/            # Utility functions
    └── constants/        # Constants and enums
```

## Key Features

- **Time Tracking**: Create and manage time blocks with customizable categories
- **Notes**: Add detailed notes to each time block
- **Analytics**: View time spent by category
- **Multiple Views**: Grid view, Todo, and week view
- **Responsive Design**: Works on mobile and desktop
- **Dark Mode**: Toggle between light and dark themes

## Technology Stack

- **Frontend**: Next.js, React, TypeScript, Tailwind CSS
- **Backend**: Next.js API Routes, MongoDB with Mongoose
- **Authentication**: JWT with HTTP-only cookies
- **UI Components**: shadcn/ui (based on Radix UI)
- **Forms**: React Hook Form with Zod validation
- **Charts**: Recharts

## Development

1. Clone the repository
2. Install dependencies: `npm install`
3. Set up environment variables (see `.env.example`)
4. Run the development server: `npm run dev`

## Code Organization

### Type Definitions

- **Client-side types**: Import from `@/lib/types`
- **Server-side types**: Import from `@/lib/server-utils`

### Utility Functions

- **Client-side utilities**: Import from `@/lib/utils`
- **Server-side utilities**: Import from `@/lib/server-utils`

### Protected Routes

The application uses a shared `ProtectedLayout` component for authenticated routes.

## Backend Architecture

The backend follows a layered architecture with clear separation of concerns:

### API Layer
Handles HTTP requests and responses
- **Routes**: Define API endpoints in the `app/api` directory
- **Middlewares**: Handle cross-cutting concerns like authentication
- **Validators**: Validate request data using Zod schemas

### Core Layer
Contains the business logic
- **Controllers**: Coordinate between API and services
- **Services**: Implement business logic
- **Repositories**: Handle data access
- **Errors**: Define custom error types

### Database Layer
Manages database connections and models
- **Models**: Define Mongoose models
- **Schemas**: Define Mongoose schemas

### Request Flow

1. Request comes in to an API route
2. Middleware processes the request (authentication, etc.)
3. Validator validates the request data
4. Controller handles the request and calls appropriate service
5. Service implements business logic and calls repository
6. Repository interacts with the database
7. Response flows back through the layers

### Error Handling

The application uses a centralized error handling approach:
1. Custom `ApiError` class for all application errors
2. Standardized error responses with error codes
3. Consistent logging of errors

### Authentication

Authentication is handled via JWT tokens:
1. Tokens are stored in HTTP-only cookies
2. Authentication middleware validates tokens
3. Protected routes require authentication
