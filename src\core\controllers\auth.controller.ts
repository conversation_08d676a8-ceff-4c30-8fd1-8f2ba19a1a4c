import { NextResponse } from 'next/server';
import { authService } from '@/core/services';
import { KeyAuthRequest, RegisterRequest, KeyRegenerationRequest } from '@/types/auth';
import { successResponse, errorResponse } from '@/utils/response';
import { ErrorCode } from '@/constants/error-codes';
import logger from '@/utils/logger';
import { ApiError } from '@/core/errors/api-error';

/**
 * Authentication controller
 */
export class AuthController {
  /**
   * Register a new user
   * @param data Registration data
   * @returns Response with success message or error
   */
  async register(data: RegisterRequest): Promise<NextResponse> {
    try {
      const result = await authService.register(data);
      return successResponse({
        message: result.message,
        accessKey: result.accessKey,
        recoveryCodes: result.recoveryCodes
      }, result.status);
    } catch (error) {
      logger.error('Registration controller error:', error);

      if (error instanceof ApiError) {
        return errorResponse(error.message, error.code, error.statusCode);
      }

      return errorResponse(
        'Server error',
        ErrorCode.INTERNAL_SERVER_ERROR,
        500
      );
    }
  }

  /**
   * Register a new user with a specific access key
   * @param data Registration data with access key
   * @returns Response with success message or error
   */
  async registerWithKey(data: { accessKey: string; name?: string }): Promise<NextResponse> {
    try {
      const result = await authService.registerWithKey(data);
      return successResponse({
        message: result.message,
        accessKey: result.accessKey,
        recoveryCodes: result.recoveryCodes
      }, result.status);
    } catch (error) {
      logger.error('Registration with key controller error:', error);

      if (error instanceof ApiError) {
        return errorResponse(error.message, error.code, error.statusCode);
      }

      return errorResponse(
        'Server error',
        ErrorCode.INTERNAL_SERVER_ERROR,
        500
      );
    }
  }

  /**
   * Authenticate user with access key
   * @param data Authentication data
   * @param req Next.js request for rate limiting
   * @returns Response with token and user data or error
   */
  async login(data: KeyAuthRequest, req?: any): Promise<NextResponse> {
    try {
      return await authService.login(data, req);
    } catch (error) {
      logger.error('Login controller error:', error);

      if (error instanceof ApiError) {
        return errorResponse(error.message, error.code, error.statusCode);
      }

      return errorResponse(
        'Server error',
        ErrorCode.INTERNAL_SERVER_ERROR,
        500
      );
    }
  }

  /**
   * Authenticate user with recovery code
   * @param data Authentication data with recovery code
   * @param req Next.js request for rate limiting
   * @returns Response with token and user data or error
   */
  async loginWithRecoveryCode(data: { recoveryCode: string; accessKey: string }, req?: any): Promise<NextResponse> {
    try {
      return await authService.loginWithRecoveryCode(data, req);
    } catch (error) {
      logger.error('Recovery code login controller error:', error);

      if (error instanceof ApiError) {
        return errorResponse(error.message, error.code, error.statusCode);
      }

      return errorResponse(
        'Server error',
        ErrorCode.INTERNAL_SERVER_ERROR,
        500
      );
    }
  }

  /**
   * Logout a user
   * @returns Response with success message
   */
  async logout(): Promise<NextResponse> {
    try {
      return authService.logout();
    } catch (error) {
      logger.error('Logout controller error:', error);
      
      if (error instanceof ApiError) {
        return errorResponse(error.message, error.code, error.statusCode);
      }
      
      return errorResponse(
        'Server error',
        ErrorCode.INTERNAL_SERVER_ERROR,
        500
      );
    }
  }

  /**
   * Get user profile
   * @param userId User ID
   * @returns Response with user profile or error
   */
  async getProfile(userId: string): Promise<NextResponse> {
    try {
      const profile = await authService.getProfile(userId);
      return successResponse(profile);
    } catch (error) {
      logger.error('Get profile controller error:', error);
      
      if (error instanceof ApiError) {
        return errorResponse(error.message, error.code, error.statusCode);
      }
      
      return errorResponse(
        'Server error',
        ErrorCode.INTERNAL_SERVER_ERROR,
        500
      );
    }
  }

  /**
   * Update user profile
   * @param userId User ID
   * @param data Profile data
   * @returns Response with updated profile or error
   */
  async updateProfile(userId: string, data: any): Promise<NextResponse> {
    try {
      const profile = await authService.updateProfile(userId, data);
      return successResponse(profile);
    } catch (error) {
      logger.error('Update profile controller error:', error);
      
      if (error instanceof ApiError) {
        return errorResponse(error.message, error.code, error.statusCode);
      }
      
      return errorResponse(
        'Server error',
        ErrorCode.INTERNAL_SERVER_ERROR,
        500
      );
    }
  }

  /**
   * Regenerate user access key
   * @param userId User ID
   * @param data Key regeneration data
   * @returns Response with new access key or error
   */
  async regenerateAccessKey(userId: string, data: KeyRegenerationRequest): Promise<NextResponse> {
    try {
      const result = await authService.regenerateAccessKey(userId, data);
      return successResponse({
        message: result.message,
        newAccessKey: result.newAccessKey
      });
    } catch (error) {
      logger.error('Regenerate access key controller error:', error);

      if (error instanceof ApiError) {
        return errorResponse(error.message, error.code, error.statusCode);
      }

      return errorResponse(
        'Server error',
        ErrorCode.INTERNAL_SERVER_ERROR,
        500
      );
    }
  }
}
