import { NextRequest } from 'next/server';
import { validateKeyRegeneration } from '@/app/api/_validators/auth.validator';
import { authController } from '@/core/controllers';
import { authMiddleware } from '@/app/api/_middleware/auth.middleware';
import logger from '@/utils/logger';
import { errorResponse } from '@/utils/response';
import { ErrorCode } from '@/constants/error-codes';
import { verifyAuth } from '@/utils/auth';

// Tell Next.js this route should always be dynamically rendered
export const dynamic = 'force-dynamic';

/**
 * PUT /api/auth/regenerate-key - Regenerate user access key
 */
export async function PUT(req: NextRequest) {
  try {
    // Check authentication
    const authResponse = authMiddleware(req);
    if (authResponse) return authResponse;

    // Get user from token
    const { user } = verifyAuth(req);
    if (!user) {
      return errorResponse('Authentication required', ErrorCode.UNAUTHORIZED, 401);
    }

    // Validate request
    const validationResult = await validateKeyRegeneration(req);
    if ('status' in validationResult) {
      return validationResult;
    }

    // Process key regeneration
    return authController.regenerateAccessKey(user.id, validationResult.data);
  } catch (error) {
    logger.error('Key regeneration error:', error);
    return errorResponse(
      'An unexpected error occurred',
      ErrorCode.INTERNAL_SERVER_ERROR,
      500
    );
  }
}
