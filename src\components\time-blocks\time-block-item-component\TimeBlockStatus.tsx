"use client"

import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { TimeBlock } from '@/lib/types';
import { Circle, CheckCircle2, Pencil, Trash } from 'lucide-react';
import { cn } from '@/lib/utils';

interface TimeBlockStatusProps {
  timeBlock: TimeBlock;
  onToggleTodo: () => void;
  onEdit: () => void;
  onDelete: () => void;
  categoryColor: string;
}

export function TimeBlockStatus({
  timeBlock,
  onToggleTodo,
  onEdit,
  onDelete,
  categoryColor
}: TimeBlockStatusProps) {
  return (
    <div className="flex items-center gap-2 mb-3">
      <Badge
        variant="outline"
        className="h-5 px-2 text-xs font-medium rounded-full"
        style={{
          backgroundColor: `${categoryColor}40`,
          color: categoryColor,
          borderColor: `${categoryColor}90`,
          borderWidth: '2px'
        }}
      >
        {timeBlock.categoryData?.name || 'Unknown Category'}
      </Badge>

      {timeBlock.isTodo && (
        <Badge
          variant={timeBlock.isCompleted ? "default" : "outline"}
          className={cn(
            "h-5 px-2 text-sm font-medium todo-status-badge",
            timeBlock.isCompleted
              ? "bg-green-500 hover:bg-green-600 border-green-600"
              : "border-muted-foreground/10 border"
          )}
        >
          {timeBlock.isCompleted ? "Done" : "Todo"}
        </Badge>
      )}

      <div className="flex items-center gap-1 ml-auto">
        {timeBlock.isTodo && (
          <Button
            variant="ghost"
            size="icon"
            className={cn(
              "h-8 w-8 rounded-full",
              timeBlock.isCompleted
                ? "text-green-600 hover:bg-green-100 border-2 border-green-500"
                : "text-muted-foreground hover:text-primary hover:bg-primary/5 border-2 border-muted-foreground/50"
            )}
            onClick={(e) => {
              e.stopPropagation();
              onToggleTodo();
            }}
          >
            {timeBlock.isCompleted ? (
              <CheckCircle2 className="h-5 w-5" />
            ) : (
              <Circle className="h-5 w-5" />
            )}
            <span className="sr-only">
              {timeBlock.isCompleted ? "Mark as incomplete" : "Mark as complete"}
            </span>
          </Button>
        )}

        <Button
          variant="ghost"
          size="icon"
          className="h-6 w-6 text-muted-foreground hover:text-foreground border border-muted-foreground/30"
          onClick={(e) => {
            e.stopPropagation();
            onEdit();
          }}
        >
          <Pencil className="h-3.5 w-3.5" />
        </Button>

        <Button
          variant="ghost"
          size="icon"
          className="h-6 w-6 text-muted-foreground hover:text-destructive border border-muted-foreground/30"
          onClick={(e) => {
            e.stopPropagation();
            onDelete();
          }}
        >
          <Trash className="h-3.5 w-3.5" />
        </Button>
      </div>
    </div>
  );
}
