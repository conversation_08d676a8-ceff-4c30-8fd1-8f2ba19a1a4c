import { NextRequest, NextResponse } from 'next/server';
import { validateUpdateTimeBlock } from '@/app/api/_validators/timeblock.validator';
import { timeBlockController } from '@/core/controllers';
import { authMiddleware } from '@/app/api/_middleware/auth.middleware';
import logger from '@/utils/logger';
import { errorResponse } from '@/utils/response';
import { ErrorCode } from '@/constants/error-codes';
import { verifyAuth } from '@/utils/auth';

export const dynamic = 'force-dynamic';

export async function PUT(
  request: NextRequest,
  context: any
) {
  try {
    const authResponse = authMiddleware(request);
    if (authResponse) return authResponse;

    const { user } = verifyAuth(request);
    if (!user) {
      return errorResponse('Authentication required', ErrorCode.UNAUTHORIZED, 401);
    }

    const params = await context.params as { id: string };
    const validationResult = await validateUpdateTimeBlock(request);
    if ('status' in validationResult) {
      return validationResult;
    }

    return timeBlockController.updateTimeBlock(params.id, user.id, validationResult.data);
  } catch (error) {
    logger.error('Update time block error:', error);
    return errorResponse(
      'An unexpected error occurred',
      ErrorCode.INTERNAL_SERVER_ERROR,
      500
    );
  }
}

export async function DELETE(
  request: NextRequest,
  context: any
) {
  try {
    const authResponse = authMiddleware(request);
    if (authResponse) return authResponse;

    const { user } = verifyAuth(request);
    if (!user) {
      return errorResponse('Authentication required', ErrorCode.UNAUTHORIZED, 401);
    }

    const params = await context.params as { id: string };
    return timeBlockController.deleteTimeBlock(params.id, user.id);
  } catch (error) {
    logger.error('Delete time block error:', error);
    return errorResponse(
      'An unexpected error occurred',
      ErrorCode.INTERNAL_SERVER_ERROR,
      500
    );
  }
}
