/**
 * Category interface
 */
export interface Category {
  id: string;
  userId: string;
  name: string;
  color: string;
  createdAt: string;
  updatedAt: string;
}

/**
 * Create Category request interface
 */
export interface CreateCategoryRequest {
  name: string;
  color: string;
}

/**
 * Update Category request interface
 */
export interface UpdateCategoryRequest {
  name?: string;
  color?: string;
}
