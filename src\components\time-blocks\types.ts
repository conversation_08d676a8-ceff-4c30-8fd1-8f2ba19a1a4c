import { UseFormReturn } from 'react-hook-form';
import { TimeBlock, TimeBlockFormData } from '@/lib/types';

export interface TimeBlockFormProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (data: TimeBlockFormData) => void;
  timeBlock?: TimeBlock;
  selectedDate: Date;
}

export interface FormComponentProps {
  form: UseFormReturn<any>;
}

export interface TimeBlockActionProps {
  onToggleTodo: () => void;
  onEdit: () => void;
  onDelete: () => void;
}


export interface TimeBlockCardProps {
  block: TimeBlock;
  onEdit: (block: TimeBlock) => void;
  onUpdate: (id: string, data: Partial<TimeBlock>) => void;
  onClick: () => void;
}

export interface TimeIndicatorProps {
  date: Date;
  timeSlots: Date[];
}
