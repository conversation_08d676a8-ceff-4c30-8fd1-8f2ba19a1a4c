"use client"

import { usePathname } from 'next/navigation';
import { useAuth } from '@/hooks/use-auth';
import Navbar from '@/components/navbar';
import React, { useEffect } from 'react';
import { Loader2 } from 'lucide-react';

interface AuthenticatedLayoutProps {
  children: React.ReactNode;
}

// Simple loading spinner component with minimal display
function LoadingSpinner() {
  return (
    <div className="flex min-h-screen items-center justify-center">
      <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
    </div>
  );
}

export default function AuthenticatedLayout({
  children,
}: AuthenticatedLayoutProps) {
  const pathname = usePathname();
  // Default redirect for protected routes
  const redirectTo = '/auth/login';
  // Allow demo paths without authentication check if needed
  const allowDemo = pathname.includes('/demo');

  const finalRedirectTo = allowDemo ? undefined : redirectTo;
  const { isAuthenticated, isLoading } = useAuth({ redirectTo: finalRedirectTo });

  // Additional safety check - if not loading and not authenticated, force redirect to root
  useEffect(() => {
    if (!isLoading && !isAuthenticated && !allowDemo) {
      // Use window.location for immediate redirect to avoid React routing issues
      console.log('Authentication failed in layout, redirecting to root page');

      // Add a small delay to ensure any ongoing auth checks complete
      const timer = setTimeout(() => {
        window.location.href = '/';
      }, 100);

      return () => clearTimeout(timer);
    }
  }, [isLoading, isAuthenticated, allowDemo]);

  // Show loading spinner while checking authentication
  if (isLoading) {
    return <LoadingSpinner />;
  }

  // If not authenticated and not on an allowed demo path, show loading while redirecting
  if (!isAuthenticated && !allowDemo) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin text-muted-foreground mx-auto mb-4" />
          <p className="text-sm text-muted-foreground">Redirecting to login...</p>
        </div>
      </div>
    );
  }

  // Render the layout with Navbar for authenticated users or allowed demo paths
  return (
    <div className="flex min-h-screen flex-col bg-muted/10">
      <Navbar />
      <main className="flex-1">
        {children}
      </main>
    </div>
  );
}
