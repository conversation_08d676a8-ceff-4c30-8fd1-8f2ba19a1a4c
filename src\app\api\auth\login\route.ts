import { NextRequest } from 'next/server';
import { keyAuthSchema, recoveryCodeAuthSchema } from '@/app/api/_validators/auth.validator';
import { authController } from '@/core/controllers';
import logger from '@/utils/logger';
import { errorResponse } from '@/utils/response';
import { ErrorCode } from '@/constants/error-codes';

// Tell Next.js this route should always be dynamically rendered
export const dynamic = 'force-dynamic';

/**
 * POST /api/auth/login - Authenticate user with access key or recovery code
 */
export async function POST(req: NextRequest) {
  try {
    // Parse request body once
    const body = await req.json().catch(() => ({}));

    // Check if this is a recovery code authentication
    if (body.recoveryCode) {
      // Validate recovery code request
      const result = recoveryCodeAuthSchema.safeParse(body);
      if (!result.success) {
        const errors = result.error.errors.map(err => `${err.path.join('.')}: ${err.message}`).join(', ');
        return errorResponse(errors, ErrorCode.VALIDATION_ERROR, 400);
      }

      // Process recovery code authentication
      return authController.loginWithRecoveryCode(result.data, req);
    } else {
      // Validate access key request
      const result = keyAuthSchema.safeParse(body);
      if (!result.success) {
        const errors = result.error.errors.map(err => `${err.path.join('.')}: ${err.message}`).join(', ');
        return errorResponse(errors, ErrorCode.VALIDATION_ERROR, 400);
      }

      // Process access key authentication
      return authController.login(result.data, req);
    }
  } catch (error) {
    logger.error('Authentication error:', error);
    return errorResponse(
      'Invalid request body',
      ErrorCode.BAD_REQUEST,
      400
    );
  }
}
